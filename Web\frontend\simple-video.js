import * as THREE from './libs/three.module.js';
import { OrbitControls } from './libs/OrbitControls.js';

/**
 * 简化双目识别系统
 * 只负责显示两个摄像头画面，无任何分析功能
 */

class SimpleCameraDisplay {
    constructor() {
        // WebSocket连接
        this.ws = null;
        this.connected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000;
        
        // DOM元素
        this.leftCanvas = document.getElementById('leftCamera');
        this.rightCanvas = document.getElementById('rightCamera');
        this.leftCtx = this.leftCanvas?.getContext('2d');
        this.rightCtx = this.rightCanvas?.getContext('2d');
        this.leftTrajectoryCanvas = document.getElementById('leftTrajectoryCanvas');
        this.rightTrajectoryCanvas = document.getElementById('rightTrajectoryCanvas');
        this.leftTrajectoryCtx = this.leftTrajectoryCanvas?.getContext('2d');
        this.rightTrajectoryCtx = this.rightTrajectoryCanvas?.getContext('2d');
        
        // 模态框元素
        this.modal = document.getElementById('modal');
        this.modalCanvas = document.getElementById('modalCanvas');
        this.modalCtx = this.modalCanvas?.getContext('2d');
        this.modalClose = document.getElementById('modalClose');
        this.enlargedCameraId = null; // 1 for left, 2 for right
        
        // 通知
        this.notificationContainer = document.getElementById('notification-container');
        
        // 状态元素
        this.statusDot = document.getElementById('statusDot');
        this.connectionStatus = document.getElementById('connection-status');
        this.fpsDisplay = document.getElementById('fps-display');
        this.dashboardFpsDisplay = document.getElementById('dashboard-fps-display');
        this.leftStatus = document.getElementById('leftCameraStatus');
        this.rightStatus = document.getElementById('rightCameraStatus');
        this.leftNoSignal = document.getElementById('leftNoSignal');
        this.rightNoSignal = document.getElementById('rightNoSignal');

        // 状态卡片
        this.websocketStatusCard = document.getElementById('websocket-status-card');
        this.cameraStatusCard = document.getElementById('camera-status-card');
        this.streamStatusCard = document.getElementById('stream-status-card');
        this.recordingStatusCard = document.getElementById('recording-status-card');
        
        // 控制按钮
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.refreshBtn = document.getElementById('refreshBtn');

        // 精彩录制控制按钮
        this.highlightStartBtn = document.getElementById('highlightStartBtn');
        this.highlightStopBtn = document.getElementById('highlightStopBtn');
        this.highlightStatus = document.getElementById('highlightStatus');
        this.highlightDuration = document.getElementById('highlightDuration');
        this.highlightMoments = document.getElementById('highlightMoments');
        this.speedThreshold = document.getElementById('speedThreshold');
        this.speedThresholdValue = document.getElementById('speedThresholdValue');
        
        // 录制相关DOM元素
        this.startRec1 = document.getElementById('startRec1');
        this.stopRec1 = document.getElementById('stopRec1');
        this.recStatus1 = document.getElementById('recStatus1');
        this.startRec2 = document.getElementById('startRec2');
        this.stopRec2 = document.getElementById('stopRec2');
        this.recStatus2 = document.getElementById('recStatus2');
        this.recordAllBtn = document.getElementById('recordAllBtn');

        // 标定相关DOM元素
        console.log('🔍 开始获取标定相关DOM元素...');
        this.calibrationBtn = document.getElementById('calibrationBtn');
        console.log('calibrationBtn 获取结果:', this.calibrationBtn);
        this.calibrationPanel = document.getElementById('calibrationPanel');
        console.log('calibrationPanel 获取结果:', this.calibrationPanel);
        this.calibrationStatusCard = document.getElementById('calibration-status-card');
        this.calibrationStatus = document.getElementById('calibrationStatus');
        this.calibrationStatusDot = document.getElementById('calibrationStatusDot');
        this.calibrationStatusText = document.getElementById('calibrationStatusText');
        this.startCalibrationBtn = document.getElementById('startCalibrationBtn');
        console.log('startCalibrationBtn 获取结果:', this.startCalibrationBtn);
        this.stopCalibrationBtn = document.getElementById('stopCalibrationBtn');
        this.calibrationProgress = document.getElementById('calibrationProgress');
        this.calibrationProgressFill = document.getElementById('calibrationProgressFill');
        this.calibrationProgressText = document.getElementById('calibrationProgressText');
        this.calibrationResults = document.getElementById('calibrationResults');
        this.calibrationResultStatus = document.getElementById('calibrationResultStatus');
        this.calibrationResultError = document.getElementById('calibrationResultError');
        this.calibrationResultCorners = document.getElementById('calibrationResultCorners');
        this.calibrationResultTime = document.getElementById('calibrationResultTime');
        
        // 阈值控制
        this.thresholdSlider = document.getElementById('confidenceThreshold');
        this.thresholdValueDisplay = document.getElementById('thresholdValue');
        
        // 三维坐标相关DOM元素
        this.coordStatus = document.getElementById('coordStatus');
        this.coordStatusDot = document.getElementById('coordStatusDot');
        this.coordStatusText = document.getElementById('coordStatusText');
        this.unitSelector = document.getElementById('unitSelector');
        this.ballCount = document.getElementById('ballCount');
        this.coordLastUpdate = document.getElementById('coordLastUpdate');
        this.coordinatesTableBody = document.getElementById('coordinatesTableBody');
        this.reconstruction3dStatus = document.getElementById('reconstruction3dStatus');
        this.ballSpeedDisplay = document.getElementById('ballSpeed');
        
        // 数据库浏览器相关DOM元素
        this.sqlQueryInput = document.getElementById('sql-query-input');
        this.executeSqlBtn = document.getElementById('execute-sql-btn');
        this.dbStatusMessage = document.getElementById('db-status-message');
        this.dbResultsContainer = document.getElementById('db-results-container');

        // 数据可视化相关DOM元素
        this.refreshChartsBtn = document.getElementById('refreshChartsBtn');
        this.exportDataBtn = document.getElementById('exportDataBtn');
        this.timeRangeSelect = document.getElementById('timeRangeSelect');
        this.speedChartStatus = document.getElementById('speedChartStatus');
        this.heatmapStatus = document.getElementById('heatmapStatus');
        this.statsStatus = document.getElementById('statsStatus');
        this.avgSpeed = document.getElementById('avgSpeed');
        this.maxSpeed = document.getElementById('maxSpeed');
        this.dataPointCount = document.getElementById('dataPointCount');
        this.activeDuration = document.getElementById('activeDuration');

        // 交互式图表相关DOM元素
        this.zoomInBtn = document.getElementById('zoomInBtn');
        this.zoomOutBtn = document.getElementById('zoomOutBtn');
        this.resetViewBtn = document.getElementById('resetViewBtn');
        this.refreshInteractiveBtn = document.getElementById('refreshInteractiveBtn');
        this.leftCameraToggle = document.getElementById('leftCameraToggle');
        this.rightCameraToggle = document.getElementById('rightCameraToggle');

        // 筛选器相关DOM元素
        this.filterToggleBtn = document.getElementById('filterToggleBtn');
        this.dataFilterPanel = document.getElementById('dataFilterPanel');
        this.applyFiltersBtn = document.getElementById('applyFiltersBtn');
        this.resetFiltersBtn = document.getElementById('resetFiltersBtn');
        this.startTimeFilter = document.getElementById('startTimeFilter');
        this.endTimeFilter = document.getElementById('endTimeFilter');
        this.minSpeedFilter = document.getElementById('minSpeedFilter');
        this.maxSpeedFilter = document.getElementById('maxSpeedFilter');
        this.cameraFilter = document.getElementById('cameraFilter');
        this.minXFilter = document.getElementById('minXFilter');
        this.maxXFilter = document.getElementById('maxXFilter');
        this.minYFilter = document.getElementById('minYFilter');
        this.maxYFilter = document.getElementById('maxYFilter');
        this.minZFilter = document.getElementById('minZFilter');
        this.maxZFilter = document.getElementById('maxZFilter');

        // 新增：检测框录制控制相关DOM元素
        this.detectionRecordingToggle = document.getElementById('detectionRecordingToggle');
        this.recordingModeSelect = document.getElementById('recordingModeSelect');
        this.detectionRecordingStatus = document.getElementById('detectionRecordingStatus');
        this.detectionRecordingStatusText = document.getElementById('detectionRecordingStatusText');

        // Chart.js 图表实例
        this.charts = {
            speedDistribution: null,
            speedTimeSeries: null,
            trajectoryHeatmap: null,
            movementFrequency: null,
            speedEcg: null
        };

        // 交互式图表实例
        this.interactiveChart = null;

        // 当前筛选条件
        this.currentFilters = {};

        // 三维轨迹可视化
        this.trajectoryVisualizer = null;
        
        // 性能统计
        this.frameCount = 0;
        this.lastFpsUpdate = Date.now();
        this.currentFps = 0;
        this.leftFrames = 0;
        this.rightFrames = 0;
        this.receiveFrameCount = 0;
        this.lastReceiveFpsUpdate = Date.now();
        
        // 渲染循环状态
        this.latestFrame1 = null;
        this.latestFrame2 = null;
        this.renderLoopActive = false;
        this.animationFrameId = null;

        // 流状态
        this.streamActive = false;
        this.isRecordingAll = false;
        this.recordingStates = { 1: false, 2: false };

        // 精彩录制状态
        this.highlightRecordingActive = false;
        this.highlightRecordingStartTime = null;
        this.highlightMomentsCount = 0;
        this.currentSpeedThreshold = 15.0;
        
        // 三维坐标状态
        this.current3DData = null;
        this.last3DUpdate = null;
        this.displayUnit = 'm'; // 默认显示单位为米

        // 标定状态
        this.calibrationActive = false;
        this.calibrationStatus = 'idle';
        
        // 2D轨迹数据
        this.trajectoryData2D = {
            1: { points: [], width: 0, height: 0 },
            2: { points: [], width: 0, height: 0 }
        };

        // 轨迹渲染配置
        this.trajectoryConfig = {
            maxAge: 3000,          // 轨迹最大存活时间（毫秒）- 设置为3秒
            maxPoints: 200,        // 最大轨迹点数
            baseLineWidth: 3,      // 基础线宽
            minOpacity: 0.1,       // 最小透明度
            glowIntensity: 0.5,    // 发光强度
            pointRadius: 4,        // 最新点的半径
            pointShowDuration: 1500, // 最新点显示时长（毫秒）- 1.5秒
            staticPointDuration: 3000, // 静止点显示时长（毫秒）- 球停止时的显示时间
            colors: {
                trajectory: [255, 87, 34],  // 轨迹颜色 (RGB)
                point: [255, 255, 255],     // 最新点颜色 (RGB)
                staticPoint: [255, 255, 0], // 静止点颜色 (RGB) - 黄色
                glow: [255, 87, 34]         // 发光颜色 (RGB)
            }
        };

        // 球的状态跟踪
        this.ballState = {
            1: { lastPosition: null, lastUpdateTime: 0, isStatic: false, ballExists: false },
            2: { lastPosition: null, lastUpdateTime: 0, isStatic: false, ballExists: false }
        };

        // 清理时间戳
        this.lastCleanupTime = 0;
        
        // 调试信息：检查所有DOM元素是否找到
        console.log('🔧 DOM元素检查:');
        console.log('  - 左画布:', this.leftCanvas ? '✅' : '❌');
        console.log('  - 右画布:', this.rightCanvas ? '✅' : '❌');
        console.log('  - 状态点:', this.statusDot ? '✅' : '❌');
        console.log('  - 连接状态:', this.connectionStatus ? '✅' : '❌');
        console.log('  - FPS显示(头部):', this.fpsDisplay ? '✅' : '❌');
        console.log('  - FPS显示(仪表板):', this.dashboardFpsDisplay ? '✅' : '❌');
        console.log('  - 控制按钮:', this.startBtn ? '✅' : '❌', this.stopBtn ? '✅' : '❌', this.refreshBtn ? '✅' : '❌');
        
        // 确保canvas有初始尺寸
        this.leftCanvas.width = 640;
        this.leftCanvas.height = 360;
        this.rightCanvas.width = 640;
        this.rightCanvas.height = 360;

        // 初始化所有状态
        this.updateWebSocketStatus('未连接', 'bad');
        this.updateCameraSystemStatus('离线', 'bad');
        this.updateStreamStatus('已停止', 'neutral');
        this.updateRecordingStatus('未录制', 'neutral');

        // 初始化FPS显示
        this.initializeFpsDisplay();

        // 初始化控制按钮状态
        this.updateButtonStates();
        
        // 初始化
        this.init();
    }
    
    init() {
        console.log('🎥 初始化简化双目识别系统');
        this.updateLastUpdateTime();
        this.connectWebSocket();
        this.setupControlHandlers();
        this.setupModalHandlers();
        this.setupThresholdHandler();
        this.setupRecordingHandlers();
        this.setupRecordAllHandler();
        this.setupHighlightRecordingHandlers();
        this.setupDetectionRecordingHandlers(); // 新增：检测框录制控制
        this.setup3DCoordinateHandlers();

        console.log('🔧 准备初始化标定处理器...');
        try {
            this.setupCalibrationHandlers();
            console.log('✅ 标定处理器初始化完成');
        } catch (error) {
            console.error('❌ 标定处理器初始化失败:', error);
        }

        this.initDbExplorer();

        // 初始化数据可视化
        this.initDataVisualization();

        // 初始化轨迹可视化
        this.initTrajectoryVisualizer();

        // 定期清理2D轨迹
        setInterval(() => this.cleanup2DTrajectories(), 50); // 每50ms清理一次

        // 调试：定期输出轨迹状态
        if (window.location.search.includes('debug=trajectory')) {
            setInterval(() => this.logTrajectoryStatus(), 2000); // 每2秒输出一次状态
        }

        // 定期更新时间
        setInterval(() => this.updateLastUpdateTime(), 60000);
    }
    
    connectWebSocket() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log('WebSocket已经连接');
            return;
        }
        
        const wsUrl = `ws://${window.location.host}/ws`;
        console.log('🔗 尝试连接WebSocket:', wsUrl);
        
        try {
            this.ws = new WebSocket(wsUrl);
            this.ws.binaryType = 'arraybuffer';
            this.setupWebSocketHandlers();
        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
            this.updateConnectionStatus('error');
            this.showNotification(`WebSocket连接失败: ${error.message}`, 'error');
            this.scheduleReconnect();
        }
    }
    
    setupWebSocketHandlers() {
        this.ws.onopen = () => {
            console.log('✅ WebSocket连接成功');
            this.connected = true;
            this.reconnectAttempts = 0;
            
            this.updateConnectionStatus('connected');
            this.updateWebSocketStatus('已连接', 'good');
            this.updateCameraSystemStatus('运行中', 'good');
            this.showNotification('WebSocket连接成功！请点击"开始视频流"按钮', 'success');
            this.updateButtonStates();
            
            // 不再自动启动视频流，由用户手动控制
        };
        
        this.ws.onmessage = (event) => {
            if (event.data instanceof ArrayBuffer) {
                // 优化：不再在此处直接处理，避免阻塞消息循环
                this.handleBinaryMessage(event.data);
            } else {
                this.handleTextMessage(event.data);
            }
        };
        
        this.ws.onclose = (event) => {
            console.log('🔌 WebSocket连接关闭:', event.code, event.reason);
            this.connected = false;
            this.streamActive = false;
            this.stopRenderLoop(); // 确保渲染循环停止
            this.updateConnectionStatus('disconnected');
            this.updateWebSocketStatus('连接断开', 'bad');
            this.updateCameraSystemStatus('离线', 'bad');
            this.updateButtonStates();
            this.hideAllSignals();
            this.initializeFpsDisplay(); // 重置FPS显示
            this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.updateConnectionStatus('error');
            this.updateWebSocketStatus('连接错误', 'bad');
        };
    }
    
    handleBinaryMessage(buffer) {
        console.log(`%c[DEBUG] handleBinaryMessage called. streamActive is: ${this.streamActive}`, 'color: orange;');
        if (!this.streamActive) {
             // 根据日志，我们有时候会在视频流停止后还收到一些延迟的帧
             // 为了避免不必要的解码和处理，我们在这里加一个检查。
            const view = new DataView(buffer);
            const cameraId = view.getUint32(0, true);
            console.log(`- 丢弃延迟帧 (摄像头ID: ${cameraId})，因为视频流已停止。`);
            return;
        }

        try {
            const view = new DataView(buffer);
            const cameraId = view.getUint32(0, true);
            const jpegData = buffer.slice(12); // 12 = 4 (ID) + 8 (timestamp)
            
            // 解码并存储帧，而不是直接绘制
            this.decodeAndStoreFrame(cameraId, jpegData);
            
            // 更新接收帧数统计 (可选，这现在代表接收率)
            this.updateFrameStats(cameraId);
            
        } catch (error) {
            console.error('❌ 二进制消息解析错误:', error);
        }
    }
    
    decodeAndStoreFrame(cameraId, jpegData) {
        const blob = new Blob([jpegData], { type: 'image/jpeg' });
        createImageBitmap(blob).then(imageBitmap => {
            if (!this.streamActive) {
                imageBitmap.close();
                return;
            }

            // [诊断日志] 打印解码后图像的尺寸
            if (imageBitmap.width === 0 || imageBitmap.height === 0) {
                console.warn(`解码摄像头 ${cameraId} 的帧成功，但图像尺寸为0x0。`);
            }

            if (cameraId === 1) {
                if (this.latestFrame1) this.latestFrame1.close();
                this.latestFrame1 = imageBitmap;
                this.hideSignal('left');
                this.updateCameraStatus('left', '已连接');
            } else if (cameraId === 2) {
                if (this.latestFrame2) this.latestFrame2.close();
                this.latestFrame2 = imageBitmap;
                this.hideSignal('right');
                this.updateCameraStatus('right', '已连接');
            }
        }).catch(err => {
            console.error(`❌ 解码摄像头 ${cameraId} 的帧失败:`, err);
        });
    }

    handleTextMessage(data) {
        try {
            const message = JSON.parse(data);
            // console.log(`%c[WebSocket IN]`, 'color: #00b894;', message);

            switch (message.type) {
                case 'welcome':
                    console.log('✅ Welcome message received:', message.connection_id);
                    break;
                case 'status_update':
                    this.updateStatusCard(this.streamStatusCard, message.stream_status, message.stream_status === 'running' ? 'good' : 'neutral');
                    if (message.recording_status) {
                        this.recordingStates = message.recording_status;
                        this.updateOverallRecordingStatus();
                    }
                    if (message.fps_data) {
                        this.updateFpsDisplay(message.fps_data);
                    }
                    if (message.fps) {
                        this.updateFpsDisplay(message.fps);
                    }
                    break;
                case 'coordinates_update':
                    this.update3DCoordinates(message);
                    break;
                case 'trajectory_update': // 3D Trajectory
                    if(this.trajectoryVisualizer) {
                        this.trajectoryVisualizer.updateTrajectory(message.trajectory);
                    }
                    break;
                case 'trajectory_2d_update': // 2D Trajectory
                    this.handleTrajectory2DUpdate(message);
                    break;
                case 'db_query_result':
                    this.renderDbResults(message);
                    break;
                case 'db_query_error':
                    this.showDbStatus(message.error, 'error');
                    break;
                case 'calibration_status':
                    this.handleCalibrationStatus(message);
                    break;
                case 'calibration_result':
                    this.handleCalibrationResult(message);
                    break;
                case 'highlight_status_update':
                case 'highlight_completed':
                    this.handleHighlightMessage(message);
                    break;
                case 'detection_recording_status':
                    this.handleDetectionRecordingStatus(message);
                    break;
                default:
                    // console.log('Unhandled message type:', message.type);
            }
        } catch (error) {
            console.error('❌ 文本消息解析或处理错误:', error, 'Data:', data);
        }
    }

    handleTrajectory2DUpdate(data) {
        const { camera_id, trajectory, original_width, original_height } = data;
        if (!camera_id || !trajectory) return;

        const currentTime = Date.now();

        // 初始化轨迹数据结构（如果不存在）
        if (!this.trajectoryData2D[camera_id]) {
            this.trajectoryData2D[camera_id] = {
                points: [],
                width: original_width,
                height: original_height,
                lastUpdateTime: 0,
                lastTrajectoryLength: 0
            };
        }

        // 更新画布尺寸信息
        this.trajectoryData2D[camera_id].width = original_width;
        this.trajectoryData2D[camera_id].height = original_height;

        const config = this.trajectoryConfig;
        const trajectoryInfo = this.trajectoryData2D[camera_id];

        // 检查是否是新轨迹的开始（轨迹长度显著减少，说明后端重新开始了新轨迹）
        const isNewTrajectory = trajectory.length < trajectoryInfo.lastTrajectoryLength * 0.5;

        if (isNewTrajectory) {
            console.log(`🔄 摄像头${camera_id}检测到新轨迹开始，清空现有轨迹`);
            trajectoryInfo.points = [];
        }

        // 记录当前轨迹长度
        trajectoryInfo.lastTrajectoryLength = trajectory.length;

        // 后端发送的是完整的轨迹数组，我们需要智能地处理它
        // 策略：只保留比现有轨迹更新的点，避免重复处理历史数据

        let newPointsToAdd = [];

        if (trajectoryInfo.points.length === 0) {
            // 如果没有现有轨迹点，添加所有点，但使用合理的时间戳
            newPointsToAdd = trajectory.map((point, index) => ({
                x: point.x,
                y: point.y,
                // 为历史点分配递减的时间戳，最新点时间戳为当前时间
                timestamp: currentTime - (trajectory.length - 1 - index) * 16 // 16ms间隔
            }));
        } else {
            // 如果有现有轨迹点，只添加新的点
            // 检查后端轨迹的最后几个点是否是新的
            const lastExistingPoint = trajectoryInfo.points[trajectoryInfo.points.length - 1];

            // 从后端轨迹的末尾开始检查新点
            for (let i = trajectory.length - 1; i >= 0; i--) {
                const point = trajectory[i];

                // 检查这个点是否已经存在（基于位置相似性）
                let isNewPoint = true;
                const checkRange = Math.min(5, trajectoryInfo.points.length); // 检查最近5个点

                for (let j = trajectoryInfo.points.length - checkRange; j < trajectoryInfo.points.length; j++) {
                    if (j >= 0) {
                        const existingPoint = trajectoryInfo.points[j];
                        const distance = Math.sqrt(
                            Math.pow(point.x - existingPoint.x, 2) +
                            Math.pow(point.y - existingPoint.y, 2)
                        );

                        if (distance < 2) { // 2像素内认为是同一个点
                            isNewPoint = false;
                            break;
                        }
                    }
                }

                if (isNewPoint) {
                    // 新点的时间戳应该比最后一个现有点更新
                    const newTimestamp = Math.max(
                        currentTime - (trajectory.length - 1 - i) * 16,
                        lastExistingPoint.timestamp + 16
                    );

                    newPointsToAdd.unshift({
                        x: point.x,
                        y: point.y,
                        timestamp: newTimestamp
                    });
                } else {
                    // 如果遇到已存在的点，停止添加更早的点
                    break;
                }
            }
        }

        // 添加新轨迹点
        if (newPointsToAdd.length > 0) {
            console.log(`📍 摄像头${camera_id}添加${newPointsToAdd.length}个新轨迹点`);
            trajectoryInfo.points.push(...newPointsToAdd);
        }

        // 清理过期的轨迹点
        const oldLength = trajectoryInfo.points.length;
        trajectoryInfo.points = trajectoryInfo.points.filter(
            point => currentTime - point.timestamp < config.maxAge
        );

        if (oldLength !== trajectoryInfo.points.length) {
            console.log(`🧹 摄像头${camera_id}清理了${oldLength - trajectoryInfo.points.length}个过期轨迹点`);
        }

        // 限制轨迹点数量，防止内存泄漏
        if (trajectoryInfo.points.length > config.maxPoints) {
            trajectoryInfo.points = trajectoryInfo.points.slice(-config.maxPoints);
            console.log(`✂️ 摄像头${camera_id}轨迹点数量超限，已裁剪至${config.maxPoints}个点`);
        }

        // 更新最后更新时间
        trajectoryInfo.lastUpdateTime = currentTime;

        // 绘制轨迹
        this.drawTrajectory(camera_id);
    }

    // 调试函数：输出轨迹状态
    logTrajectoryStatus() {
        const currentTime = Date.now();
        console.log('=== 轨迹状态报告 ===');

        for (const cameraId in this.trajectoryData2D) {
            const trajectoryInfo = this.trajectoryData2D[cameraId];
            const ballState = this.ballState[cameraId];

            if (trajectoryInfo && trajectoryInfo.points) {
                const validPoints = trajectoryInfo.points.filter(
                    point => currentTime - point.timestamp < this.trajectoryConfig.maxAge
                );

                const staticPoints = trajectoryInfo.points.filter(point => point.isStatic);

                console.log(`摄像头${cameraId}:`);
                console.log(`  - 总轨迹点数: ${trajectoryInfo.points.length}`);
                console.log(`  - 有效轨迹点数: ${validPoints.length}`);
                console.log(`  - 静止点数: ${staticPoints.length}`);
                console.log(`  - 画布尺寸: ${trajectoryInfo.width}x${trajectoryInfo.height}`);

                if (ballState) {
                    console.log(`  - 球状态: ${ballState.isStatic ? '静止' : '移动'}`);
                    console.log(`  - 最后更新: ${currentTime - ballState.lastUpdateTime}ms前`);
                }

                if (validPoints.length > 0) {
                    const oldestAge = currentTime - validPoints[0].timestamp;
                    const newestAge = currentTime - validPoints[validPoints.length - 1].timestamp;
                    console.log(`  - 最旧点年龄: ${oldestAge}ms`);
                    console.log(`  - 最新点年龄: ${newestAge}ms`);

                    const latestPoint = validPoints[validPoints.length - 1];
                    if (latestPoint.isStatic) {
                        const staticAge = currentTime - latestPoint.staticStartTime;
                        console.log(`  - 静止点年龄: ${staticAge}ms`);
                    }
                }
            }
        }
        console.log('==================');
    }

    drawTrajectory(cameraId) {
        const trajectoryInfo = this.trajectoryData2D[cameraId];
        if (!trajectoryInfo) return;

        let canvas, ctx;
        if (cameraId === 1) {
            canvas = this.leftTrajectoryCanvas;
            ctx = this.leftTrajectoryCtx;
        } else if (cameraId === 2) {
            canvas = this.rightTrajectoryCanvas;
            ctx = this.rightTrajectoryCtx;
        } else {
            return;
        }

        if (!canvas || !ctx) {
            console.warn(`轨迹Canvas或Context未找到，摄像头ID: ${cameraId}`);
            return;
        }

        // 确保Canvas尺寸与显示尺寸一致
        const rect = canvas.getBoundingClientRect();
        const displayWidth = Math.round(rect.width);
        const displayHeight = Math.round(rect.height);

        if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
            canvas.width = displayWidth;
            canvas.height = displayHeight;
            console.log(`更新摄像头${cameraId}轨迹Canvas尺寸: ${displayWidth}x${displayHeight}`);
        }

        // 完全清空Canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 如果没有轨迹点或轨迹点太少，直接返回
        if (!trajectoryInfo.points || trajectoryInfo.points.length < 2) {
            return;
        }

        const scaleX = canvas.width / trajectoryInfo.width;
        const scaleY = canvas.height / trajectoryInfo.height;
        const currentTime = Date.now();
        const config = this.trajectoryConfig;

        // 过滤有效的轨迹点
        const validPoints = trajectoryInfo.points.filter(
            point => point && typeof point.x === 'number' && typeof point.y === 'number' &&
                     currentTime - point.timestamp < config.maxAge
        );

        if (validPoints.length < 2) {
            return;
        }

        // 设置绘制样式
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // 绘制轨迹线段，使用更平滑的渐变效果
        for (let i = 1; i < validPoints.length; i++) {
            const prevPoint = validPoints[i - 1];
            const currPoint = validPoints[i];

            // 计算当前点的年龄和透明度（使用平滑的缓动函数）
            const age = currentTime - currPoint.timestamp;
            const normalizedAge = Math.min(age / config.maxAge, 1);

            // 使用缓动函数实现更自然的淡出效果
            const easeOut = 1 - Math.pow(normalizedAge, 2); // 二次缓出
            const opacity = Math.max(config.minOpacity, easeOut);

            // 计算线宽（使用更平滑的变化）
            const lineWidth = Math.max(1, config.baseLineWidth * Math.pow(opacity, 0.5));

            // 设置颜色和样式
            const [r, g, b] = config.colors.trajectory;
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
            ctx.lineWidth = lineWidth;

            // 添加发光效果
            const [gr, gg, gb] = config.colors.glow;
            ctx.shadowColor = `rgba(${gr}, ${gg}, ${gb}, ${opacity * config.glowIntensity})`;
            ctx.shadowBlur = 3 * opacity;

            // 绘制线段
            ctx.beginPath();
            ctx.moveTo(prevPoint.x * scaleX, prevPoint.y * scaleY);
            ctx.lineTo(currPoint.x * scaleX, currPoint.y * scaleY);
            ctx.stroke();
        }

        // 重置阴影设置
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;

        // 绘制最新点和静止点
        if (validPoints.length > 0) {
            const latestPoint = validPoints[validPoints.length - 1];
            const age = currentTime - latestPoint.timestamp;

            // 检查是否是静止点
            if (latestPoint.isStatic) {
                // 静止点的特殊渲染
                const staticAge = currentTime - latestPoint.staticStartTime;
                if (staticAge < config.staticPointDuration) {
                    const staticOpacity = Math.max(0.4, 1 - (staticAge / config.staticPointDuration));
                    const [sr, sg, sb] = config.colors.staticPoint;

                    // 绘制静止点的脉动效果
                    const pulseScale = 1 + 0.3 * Math.sin(currentTime * 0.01); // 脉动效果

                    // 外圈（发光效果）
                    ctx.fillStyle = `rgba(${sr}, ${sg}, ${sb}, ${staticOpacity * 0.4})`;
                    ctx.beginPath();
                    ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * 2 * pulseScale * staticOpacity, 0, 2 * Math.PI);
                    ctx.fill();

                    // 中圈
                    ctx.fillStyle = `rgba(${sr}, ${sg}, ${sb}, ${staticOpacity * 0.7})`;
                    ctx.beginPath();
                    ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * 1.3 * staticOpacity, 0, 2 * Math.PI);
                    ctx.fill();

                    // 内圈（实心点）
                    ctx.fillStyle = `rgba(${sr}, ${sg}, ${sb}, ${staticOpacity})`;
                    ctx.beginPath();
                    ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * staticOpacity, 0, 2 * Math.PI);
                    ctx.fill();
                }
            } else {
                // 普通最新点的渲染
                if (age < config.pointShowDuration) {
                    const pointOpacity = Math.max(0.3, 1 - (age / config.pointShowDuration));
                    const [pr, pg, pb] = config.colors.point;

                    // 绘制外圈（发光效果）
                    ctx.fillStyle = `rgba(${pr}, ${pg}, ${pb}, ${pointOpacity * 0.3})`;
                    ctx.beginPath();
                    ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * 1.5 * pointOpacity, 0, 2 * Math.PI);
                    ctx.fill();

                    // 绘制内圈（实心点）
                    ctx.fillStyle = `rgba(${pr}, ${pg}, ${pb}, ${pointOpacity})`;
                    ctx.beginPath();
                    ctx.arc(latestPoint.x * scaleX, latestPoint.y * scaleY, config.pointRadius * pointOpacity, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
        }
    }

    cleanup2DTrajectories() {
        const currentTime = Date.now();
        const config = this.trajectoryConfig;
        let needsRedraw = false;

        // 清理每个摄像头的2D轨迹
        for (const cameraIdStr in this.trajectoryData2D) {
            const cameraId = parseInt(cameraIdStr);
            const trajectoryInfo = this.trajectoryData2D[cameraId];

            if (!trajectoryInfo || !trajectoryInfo.points) {
                continue;
            }

            const oldLength = trajectoryInfo.points.length;

            // 清理过期的轨迹点（静止点有特殊处理）
            trajectoryInfo.points = trajectoryInfo.points.filter(point => {
                if (!point || typeof point.timestamp !== 'number') {
                    return false; // 移除无效点
                }

                // 静止点使用不同的过期时间
                if (point.isStatic && point.staticStartTime) {
                    const staticAge = currentTime - point.staticStartTime;
                    return staticAge < config.staticPointDuration;
                }

                // 普通轨迹点使用标准过期时间
                return currentTime - point.timestamp < config.maxAge;
            });

            const newLength = trajectoryInfo.points.length;

            // 如果有轨迹点被清理，需要重绘
            if (oldLength !== newLength) {
                needsRedraw = true;
                this.drawTrajectory(cameraId);

                // 如果轨迹完全清空，记录日志
                if (newLength === 0 && oldLength > 0) {
                    console.log(`摄像头${cameraId}的轨迹已完全清空 (清理了${oldLength}个点)`);
                }
            }
        }

        // 定期清理和优化
        if (needsRedraw) {
            const now = Date.now();
            if (!this.lastCleanupTime || now - this.lastCleanupTime > 5000) { // 5秒清理一次
                this.lastCleanupTime = now;

                for (const cameraIdStr in this.trajectoryData2D) {
                    const trajectoryInfo = this.trajectoryData2D[cameraIdStr];
                    if (trajectoryInfo && trajectoryInfo.points) {
                        // 如果轨迹点数量超过最大限制，保留最新的点
                        if (trajectoryInfo.points.length > config.maxPoints) {
                            trajectoryInfo.points = trajectoryInfo.points.slice(-config.maxPoints);
                            console.log(`摄像头${cameraIdStr}轨迹点数量超限，已裁剪至${config.maxPoints}个点`);
                        }
                    }
                }
            }
        }
    }
    
    updateFrameStats(cameraId) {
        if (cameraId === 1) this.leftFrames++;
        if (cameraId === 2) this.rightFrames++;
        
        const now = Date.now();
        if (now - this.lastReceiveFpsUpdate > 1000) {
            const receivedFps = this.leftFrames + this.rightFrames;
            
            // 更新主FPS显示为接收到的帧率
            this.currentFps = receivedFps;
            if (this.fpsDisplay) {
                this.fpsDisplay.textContent = `${this.currentFps} FPS`;
            }
            if (this.dashboardFpsDisplay) {
                this.dashboardFpsDisplay.textContent = `${this.currentFps} FPS`;
            }

            console.log(`[DEBUG] Frames received in last second: ${receivedFps} (L: ${this.leftFrames}, R: ${this.rightFrames})`);
            
            this.leftFrames = 0;
            this.rightFrames = 0;
            this.lastReceiveFpsUpdate = now;
        }
    }

    startRenderLoop() {
        if (this.renderLoopActive) return;
        console.log('✨ 启动渲染循环');
        this.renderLoopActive = true;
        
        const loop = () => {
            if (!this.renderLoopActive) return;
            
            this.render();
            
            // FPS计算和显示逻辑已移至 updateFrameStats，以反映接收帧率而非渲染帧率
            
            this.animationFrameId = requestAnimationFrame(loop);
        };
        
        this.animationFrameId = requestAnimationFrame(loop);
    }
    
    stopRenderLoop() {
        if (!this.renderLoopActive) return;
        console.log('✋ 停止渲染循环');
        this.renderLoopActive = false;
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        // 清理最后一帧，避免下次开始时闪烁
        if(this.latestFrame1) {
            this.latestFrame1.close();
            this.latestFrame1 = null;
        }
        if(this.latestFrame2) {
            this.latestFrame2.close();
            this.latestFrame2 = null;
        }
    }
    
    render() {
        if (this.latestFrame1) {
            this.leftCtx.drawImage(this.latestFrame1, 0, 0, this.leftCanvas.width, this.leftCanvas.height);
             if (this.enlargedCameraId === 1) {
                this.modalCtx.drawImage(this.latestFrame1, 0, 0, this.modalCanvas.width, this.modalCanvas.height);
            }
        }
        
        if (this.latestFrame2) {
            this.rightCtx.drawImage(this.latestFrame2, 0, 0, this.rightCanvas.width, this.rightCanvas.height);
            if (this.enlargedCameraId === 2) {
                this.modalCtx.drawImage(this.latestFrame2, 0, 0, this.modalCanvas.width, this.modalCanvas.height);
            }
        }
    }
    
    showSignal(side) {
        if (side === 'left' && this.leftNoSignal) {
            this.leftNoSignal.style.display = 'none';
        } else if (side === 'right' && this.rightNoSignal) {
            this.rightNoSignal.style.display = 'none';
        }
    }
    
    hideSignal(side) {
        if (side === 'left' && this.leftNoSignal) {
            this.leftNoSignal.style.display = 'none';
        } else if (side === 'right' && this.rightNoSignal) {
            this.rightNoSignal.style.display = 'none';
        }
    }
    
    hideAllSignals() {
        this.hideSignal('left');
        this.hideSignal('right');
        this.updateCameraStatus('left', '等待连接');
        this.updateCameraStatus('right', '等待连接');
    }
    
    updateCameraStatus(side, status) {
        const statusElement = side === 'left' ? this.leftStatus : this.rightStatus;
        if (statusElement) {
            const textElement = statusElement.querySelector('.status-text');
            if (textElement) {
                textElement.textContent = status;
            }
            
            // 动态更新CSS类
            statusElement.classList.remove('status-connected', 'status-stopped', 'status-error', 'status-disconnected');
            switch (status.toLowerCase()) {
                case '已连接':
                    statusElement.classList.add('status-connected');
                    break;
                case '已停止':
                    statusElement.classList.add('status-stopped');
                    break;
                 case '等待连接':
                    statusElement.classList.add('status-disconnected');
                    break;
                case '错误':
                    statusElement.classList.add('status-error');
                    break;
            }
        }
    }
    
    updateConnectionStatus(status) {
        if (!this.statusDot || !this.connectionStatus) return;
        if (status === 'connected') {
            this.statusDot.classList.add('online');
            this.connectionStatus.textContent = '已连接';
        } else {
            this.statusDot.classList.remove('online');
            this.connectionStatus.textContent = status === 'error' ? '错误' : '连接中...';
        }
    }
    
    updateWebSocketStatus(text, status) {
        this.updateStatusCard(this.websocketStatusCard, text, status);
    }
    
    updateCameraSystemStatus(text, status) {
        this.updateStatusCard(this.cameraStatusCard, text, status);
    }
    
    updateStreamStatus(text, status) {
        this.updateStatusCard(this.streamStatusCard, text, status);
    }

    updateRecordingStatus(text, status) {
        this.updateStatusCard(this.recordingStatusCard, text, status);
    }

    updateReconstructionStatus(text, status) {
        this.updateStatusCard(this.reconstructionStatusCard, text, status);
    }
    
    updateLastUpdateTime() {
        const lastUpdateEl = document.getElementById('lastUpdate');
        if (lastUpdateEl) {
            lastUpdateEl.textContent = new Date().toLocaleString('zh-CN');
        }
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay / 1000}s`);
            setTimeout(() => this.connectWebSocket(), this.reconnectDelay);
        } else {
            console.error('❌ 已达到最大重连次数，请检查服务器或网络。');
            this.showNotification('无法连接到服务器。请刷新页面重试。', 'error');
        }
    }
    
    showNotification(message, type = 'info') {
        if (!this.notificationContainer) return;
    
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
    
        this.notificationContainer.appendChild(notification);
    
        // Trigger the animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    
        // Remove the notification after some time
        setTimeout(() => {
            notification.classList.remove('show');
            notification.addEventListener('transitionend', () => {
                notification.remove();
            });
        }, 5000);
    }

    initializeFpsDisplay() {
        // 初始化所有FPS显示为0
        if (this.fpsDisplay) {
            this.fpsDisplay.textContent = '0 FPS';
        }
        if (this.dashboardFpsDisplay) {
            this.dashboardFpsDisplay.textContent = '0 FPS';
        }

        // 初始化摄像头FPS显示
        const fpsCam1 = document.getElementById('fps-cam1');
        const fpsCam2 = document.getElementById('fps-cam2');
        if (fpsCam1) {
            fpsCam1.textContent = '-- FPS';
        }
        if (fpsCam2) {
            fpsCam2.textContent = '-- FPS';
        }
    }



    updateButtonStates() {
        const connected = this.connected;
        const streamActive = this.streamActive;

        this.startBtn.disabled = !connected || streamActive;
        this.stopBtn.disabled = !connected || !streamActive;
        this.recordAllBtn.disabled = !connected || !streamActive;
        // 阈值滑块也应在流停止时禁用
        this.thresholdSlider.disabled = !connected || !streamActive;
    }
    
    setupModalHandlers() {
        if (!this.modal || !this.modalClose || !this.leftCanvas || !this.rightCanvas) {
            console.error("Modal or canvas elements not found, cannot setup handlers.");
            return;
        }

        this.modalClose.onclick = () => this.closeModal();
        
        // 点击模态框背景关闭
        this.modal.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.closeModal();
            }
        });
        
        // 按ESC键关闭
        window.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });

        this.leftCanvas.onclick = () => this.openModal(1);
        this.rightCanvas.onclick = () => this.openModal(2);
    }

    openModal(cameraId) {
        if (!this.modal || !this.modalCanvas || !this.modalCtx) return;

        const sourceCanvas = (cameraId === 1) ? this.leftCanvas : this.rightCanvas;
        if (!sourceCanvas) return;

        // 根据源Canvas的宽高比，调整模态框中Canvas的尺寸以避免拉伸
        const aspectRatio = sourceCanvas.width / sourceCanvas.height;
        // 使用窗口尺寸来计算，更准确
        const maxWidth = window.innerWidth * 0.9;
        const maxHeight = window.innerHeight * 0.9;

        let newWidth = maxWidth;
        let newHeight = newWidth / aspectRatio;

        if (newHeight > maxHeight) {
            newHeight = maxHeight;
            newWidth = newHeight * aspectRatio;
        }

        this.modalCanvas.width = newWidth;
        this.modalCanvas.height = newHeight;
        
        // 立即将当前内容绘制到模态框
        this.modalCtx.drawImage(sourceCanvas, 0, 0, this.modalCanvas.width, this.modalCanvas.height);

        this.enlargedCameraId = cameraId;
        this.modal.classList.add('show');
    }

    closeModal() {
        if (!this.modal) return;
        this.modal.classList.remove('show');
        this.enlargedCameraId = null;
    }

    setupThresholdHandler() {
        if (!this.thresholdSlider || !this.thresholdValueDisplay) return;
        
        this.thresholdSlider.addEventListener('input', () => {
            this.thresholdValueDisplay.textContent = parseFloat(this.thresholdSlider.value).toFixed(2);
        });
        
        this.thresholdSlider.addEventListener('change', () => {
            const threshold = parseFloat(this.thresholdSlider.value);
            this.sendThresholdUpdate(threshold);
        });
    }

    setupRecordingHandlers() {
        this.startRec1?.addEventListener('click', () => this.startRecording(1));
        this.stopRec1?.addEventListener('click', () => this.stopRecording(1));
        this.startRec2?.addEventListener('click', () => this.startRecording(2));
        this.stopRec2?.addEventListener('click', () => this.stopRecording(2));
    }

    async startRecording(cameraId) {
        if (!this.streamActive) {
            this.showNotification('请先启动视频流', 'warn');
            return;
        }
        console.log(`🎥 请求开始录制摄像头 ${cameraId}`);
        try {
            const response = await fetch(`/api/start_recording?camera_id=${cameraId}`, { method: 'POST' });
            if (response.ok) {
                this.showNotification(`摄像头 ${cameraId} 开始录制`, 'success');
                this.setRecordingUI(cameraId, true);
                this.recordingStates[cameraId] = true;
                this.updateOverallRecordingStatus();
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }
        } catch (error) {
            console.error(`❌ 开始录制摄像头 ${cameraId} 失败:`, error);
            this.showNotification(`开始录制失败: ${error.message}`, 'error');
        }
    }

    async stopRecording(cameraId) {
        console.log(`⏹️ 请求停止录制摄像头 ${cameraId}`);
        try {
            const response = await fetch(`/api/stop_recording?camera_id=${cameraId}`, { method: 'POST' });
            if (response.ok) {
                this.showNotification(`摄像头 ${cameraId} 停止录制`, 'info');
                this.setRecordingUI(cameraId, false);
                this.recordingStates[cameraId] = false;
                this.updateOverallRecordingStatus();
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }
        } catch (error) {
            console.error(`❌ 停止录制摄像头 ${cameraId} 失败:`, error);
            this.showNotification(`停止录制失败: ${error.message}`, 'error');
        }
    }

    setRecordingUI(cameraId, isRecording) {
        const startBtn = document.getElementById(`startRec${cameraId}`);
        const stopBtn = document.getElementById(`stopRec${cameraId}`);
        const statusDiv = document.getElementById(`recStatus${cameraId}`);
        const cameraItem = document.getElementById(`camera-item-${cameraId}`);

        if (!startBtn || !stopBtn || !statusDiv || !cameraItem) {
            console.error(`UI elements for camera ${cameraId} not found.`);
            return;
        }

        if (isRecording) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-block';
            statusDiv.querySelector('span').textContent = '录制中';
            cameraItem.classList.add('is-recording');
        } else {
            startBtn.style.display = 'inline-block';
            stopBtn.style.display = 'none';
            statusDiv.querySelector('span').textContent = '未录制';
            cameraItem.classList.remove('is-recording');
        }
        this.recordingStates[cameraId] = isRecording;
        this.updateOverallRecordingStatus();
    }
    
    sendThresholdUpdate(threshold) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                command: 'update_threshold',
                value: parseFloat(threshold)
            }));
            console.log(`📤 发送阈值更新: ${threshold}`);
        }
    }

    setupRecordAllHandler() {
        if (this.recordAllBtn) {
            this.recordAllBtn.addEventListener('click', () => this.toggleRecordAll());
        }
    }

    async toggleRecordAll() {
        if (!this.connected) {
            this.showNotification('无法操作：WebSocket未连接', 'error');
            return;
        }
        this.isRecordingAll = !this.isRecordingAll;
        console.log(`📹 请求全部录制: ${this.isRecordingAll}`);
        
        this.setRecordAllUI(this.isRecordingAll);
        
        const action = this.isRecordingAll ? 'start' : 'stop';
        this.showNotification(`请求 ${this.isRecordingAll ? '开始' : '停止'} 全部录制`, 'info');

        try {
            const response = await fetch(`/api/${action}_all_recording`, { method: 'POST' });
            if (response.ok) {
                this.showNotification(`成功${this.isRecordingAll ? '开始' : '停止'}全部录制`, 'success');
                // 更新单个UI
                this.setRecordingUI(1, this.isRecordingAll);
                this.setRecordingUI(2, this.isRecordingAll);
                this.recordingStates[1] = this.isRecordingAll;
                this.recordingStates[2] = this.isRecordingAll;
                this.updateOverallRecordingStatus();
            } else {
                // 如果失败，回滚UI状态
                this.isRecordingAll = !this.isRecordingAll;
                this.setRecordAllUI(this.isRecordingAll);
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}`);
            }
        } catch (error) {
            this.showNotification(`全部录制操作失败: ${error.message}`, 'error');
            console.error('❌ 全部录制操作失败:', error);
             // 如果失败，回滚UI状态
            this.isRecordingAll = !this.isRecordingAll;
            this.setRecordAllUI(this.isRecordingAll);
        }
    }

    setRecordAllUI(isRecording) {
        if (!this.recordAllBtn) return;

        this.isRecordingAll = isRecording; // Ensure state is synchronized

        if (isRecording) {
            this.recordAllBtn.innerHTML = '<i class="fas fa-stop-circle"></i> 全部停止';
            this.recordAllBtn.classList.add('recording-all');
        } else {
            this.recordAllBtn.innerHTML = '<i class="fas fa-video"></i> 全部录制';
            this.recordAllBtn.classList.remove('recording-all');
        }
    }

    updateStatusCard(cardElement, text, status) {
        if (!cardElement) return;
        const valueElement = cardElement.querySelector('.status-value');
        if (valueElement) {
            valueElement.textContent = text;
        }
        // status can be 'good', 'bad', 'warn', 'neutral'
        cardElement.className = `status-card status-${status}`;
    }

    updateOverallRecordingStatus() {
        const isAnyRecording = Object.values(this.recordingStates).some(state => state);
        this.updateRecordingStatus(isAnyRecording ? '录制中' : '未录制', isAnyRecording ? 'rec' : 'neutral');
        this.setRecordAllUI(this.isRecordingAll);
    }

    updateFpsDisplay(fpsMap) {
        if (!fpsMap) return;

        for (const camId in fpsMap) {
            const fps = fpsMap[camId];
            const fpsEl = document.getElementById(`fps-cam${camId}`);

            if (fpsEl) {
                fpsEl.textContent = `${fps.toFixed(1)} FPS`;
            }
        }
    }

    updateBallSpeed(speed) {
        const speedEl = document.getElementById('ballSpeed');
        if(speedEl){
            speedEl.textContent = `${speed.toFixed(2)} m/s`;
        }

        // 同时更新心电图的实时数据
        this.updateEcgRealTimeData(speed);
    }

    updateEcgRealTimeData(speed) {
        if (!this.charts.speedEcg || !this.ecgData) return;

        const currentTime = Date.now();
        const relativeTime = (currentTime % 30000) / 1000; // 30秒循环

        // 添加新的数据点
        this.ecgData.timeLabels.push(relativeTime.toFixed(1));
        this.ecgData.speedValues.push(speed);

        // 保持数据点数量在限制范围内
        if (this.ecgData.timeLabels.length > this.ecgData.maxDataPoints) {
            this.ecgData.timeLabels.shift();
            this.ecgData.speedValues.shift();
        }

        // 更新当前速度显示
        this.ecgData.currentSpeed = speed;
        this.updateCurrentSpeedDisplay(speed);

        // 更新图表（使用节流避免过于频繁的更新）
        if (!this.ecgUpdateThrottle) {
            this.ecgUpdateThrottle = this.throttle(() => {
                this.charts.speedEcg.data.labels = [...this.ecgData.timeLabels];
                this.charts.speedEcg.data.datasets[0].data = [...this.ecgData.speedValues];
                this.charts.speedEcg.update('none');
            }, 100); // 每100ms最多更新一次
        }

        this.ecgUpdateThrottle();
    }

    setup3DCoordinateHandlers() {
        if (this.unitSelector) {
            this.unitSelector.addEventListener('change', (event) => {
                this.displayUnit = event.target.value;
                console.log('切换坐标显示单位为:', this.displayUnit);
                // 如果有当前数据，重新刷新显示
                if (this.current3DData) {
                    this.refreshCoordinatesTable(this.current3DData);
                }
            });
        }
    }

    update3DCoordinates(message) {
        this.last3DUpdate = new Date();
        this.coordLastUpdate.textContent = this.last3DUpdate.toLocaleTimeString();
        this.current3DData = message.balls || [];

        // 添加调试信息
        console.log('🎯 收到3D坐标数据:', this.current3DData);

        if (this.trajectoryVisualizer) {
            console.log('🌌 更新3D轨迹可视化器');
            this.trajectoryVisualizer.updateLiveTrajectory(this.current3DData);
        } else {
            console.warn('⚠️ 3D轨迹可视化器未初始化');
        }

        // 新增：基于3D坐标更新球的状态，用于2D轨迹显示
        this.updateBallStateFrom3D(this.current3DData);

        if (this.current3DData.length > 0) {
            this.update3DStatus('good', '正在接收数据');
        } else {
            this.update3DStatus('warn', '无有效三维目标');
        }

        this.ballCount.textContent = this.current3DData.length;
        this.refreshCoordinatesTable(this.current3DData);

        // 新增：在收到3D坐标的同时更新球速
        if (message.ball_speed !== undefined) {
            this.updateBallSpeed(message.ball_speed);
        }
    }

    // 新增：基于3D坐标更新球的状态
    updateBallStateFrom3D(balls) {
        const currentTime = Date.now();

        // 检查球是否存在
        const ballExists = balls && balls.length > 0;

        if (ballExists) {
            const ball = balls[0]; // 假设只追踪第一个球

            // 更新所有摄像头的球状态（因为3D坐标对应所有摄像头）
            for (const cameraId in this.ballState) {
                const state = this.ballState[cameraId];
                const ballTimestamp = ball.timestamp; // 使用后端的时间戳

                // 检查球是否移动
                if (state.lastPosition) {
                    const distance = Math.sqrt(
                        Math.pow(ball.x - state.lastPosition.x, 2) +
                        Math.pow(ball.y - state.lastPosition.y, 2) +
                        Math.pow(ball.z - state.lastPosition.z, 2)
                    );

                    // 如果移动距离大于阈值，认为球在移动
                    const movementThreshold = 0.01; // 1cm
                    state.isStatic = distance < movementThreshold;
                } else {
                    // 第一次检测到球，认为是移动的
                    state.isStatic = false;
                }

                state.lastPosition = { x: ball.x, y: ball.y, z: ball.z };
                state.lastUpdateTime = ballTimestamp;
                state.ballExists = true; // 标记球存在

                // 如果球在移动或刚停止，更新2D轨迹的静止点
                this.updateStaticPointFor2D(parseInt(cameraId), ball, ballTimestamp);
            }
        } else {
            // 球消失了，清理所有静止点并标记球不存在
            console.log('🎾 球已消失，清理静止点');
            for (const cameraId in this.ballState) {
                const state = this.ballState[cameraId];
                state.ballExists = false; // 标记球不存在
                state.isStatic = false;

                // 清理该摄像头的所有静止点
                this.clearStaticPointsFor2D(parseInt(cameraId));
            }
        }
    }

    // 新增：为2D轨迹添加静止点
    updateStaticPointFor2D(cameraId, ball3D, timestamp) {
        if (!this.trajectoryData2D[cameraId]) {
            return;
        }

        const trajectoryInfo = this.trajectoryData2D[cameraId];
        const currentTime = Date.now();
        const ballState = this.ballState[cameraId];

        // 只有当球存在且静止时才添加静止点
        if (ballState.ballExists && ballState.isStatic) {
            // 检查是否已经有静止点
            const hasStaticPoint = trajectoryInfo.points.some(point => point.isStatic);

            if (!hasStaticPoint && trajectoryInfo.points.length > 0) {
                // 基于最后一个轨迹点创建静止点
                const lastPoint = trajectoryInfo.points[trajectoryInfo.points.length - 1];
                const staticPoint = {
                    x: lastPoint.x,
                    y: lastPoint.y,
                    timestamp: currentTime,
                    isStatic: true, // 标记为静止点
                    staticStartTime: currentTime // 静止开始时间
                };

                trajectoryInfo.points.push(staticPoint);
                this.drawTrajectory(cameraId);

                console.log(`🟡 为摄像头${cameraId}添加静止点:`, staticPoint);
            }
        } else if (!ballState.ballExists) {
            // 如果球不存在，清理静止点
            this.clearStaticPointsFor2D(cameraId);
        }
    }

    // 新增：清理指定摄像头的静止点
    clearStaticPointsFor2D(cameraId) {
        if (!this.trajectoryData2D[cameraId]) {
            return;
        }

        const trajectoryInfo = this.trajectoryData2D[cameraId];
        const oldLength = trajectoryInfo.points.length;

        // 移除所有静止点
        trajectoryInfo.points = trajectoryInfo.points.filter(point => !point.isStatic);

        const newLength = trajectoryInfo.points.length;
        if (oldLength !== newLength) {
            console.log(`🧹 清理摄像头${cameraId}的静止点: ${oldLength - newLength}个`);
            this.drawTrajectory(cameraId);
        }
    }

    refreshCoordinatesTable(data) {
        if (!data || data.length === 0) {
            // 清空现有内容
            this.coordinatesTableBody.innerHTML = '';
            return;
        }

        // 清空现有内容
        this.coordinatesTableBody.innerHTML = '';

        if (data.length === 0) {
            // 显示无数据行
            const noDataRow = document.createElement('tr');
            noDataRow.className = 'no-data-row';
            noDataRow.innerHTML = `
                <td colspan="6">
                    <i class="fas fa-info-circle"></i>
                    暂无三维坐标数据
                </td>
            `;
            this.coordinatesTableBody.appendChild(noDataRow);
            return;
        }

        // 为每个球创建表格行
        data.forEach((ball, index) => {
            const row = document.createElement('tr');
            row.className = 'coordinate-row';
            
            // 格式化坐标值
            const x = this.format3DValue(ball.x);
            const y = this.format3DValue(ball.y);
            const z = this.format3DValue(ball.z);
            const confidence = (ball.confidence * 100).toFixed(1);
            const timestamp = this.formatTimestamp(new Date(ball.timestamp));
            
            row.innerHTML = `
                <td class="coord-id">${ball.id}</td>
                <td class="coord-x">${x} ${this.displayUnit}</td>
                <td class="coord-y">${y} ${this.displayUnit}</td>
                <td class="coord-z">${z} ${this.displayUnit}</td>
                <td class="coord-confidence">${confidence}%</td>
                <td class="coord-timestamp">${timestamp}</td>
            `;
            
            // 添加置信度颜色指示
            const confidenceCell = row.querySelector('.coord-confidence');
            if (ball.confidence >= 0.8) {
                confidenceCell.classList.add('high-confidence');
            } else if (ball.confidence >= 0.6) {
                confidenceCell.classList.add('medium-confidence');
            } else {
                confidenceCell.classList.add('low-confidence');
            }
            
            this.coordinatesTableBody.appendChild(row);
        });

        this.ballCount.textContent = data.length.toString();

        if (data.length > 0) {
            this.update3DStatus('good', `检测到 ${data.length} 个目标`);
        } else {
            this.update3DStatus('warn', '未检测到目标');
        }
    }

    format3DValue(value) {
        if (this.displayUnit === 'mm') {
            // 转换为毫米（假设输入值为米）
            return (value * 1000).toFixed(1);
        } else {
            // 保持米为单位
            return value.toFixed(3);
        }
    }

    update3DStatus(status, message) {
        if (!this.coordStatusDot || !this.coordStatusText) return;
        
        this.coordStatusDot.className = `status-dot status-${status}`;
        this.coordStatusText.textContent = message;
    }

    formatTimestamp(date) {
        return date.toLocaleTimeString('zh-CN', { hour12: false });
    }

    // --- 新增：数据库浏览器功能 ---
    initDbExplorer() {
        if (!this.executeSqlBtn) {
            console.warn('DB Explorer button not found, skipping initialization.');
            return;
        }

        this.executeSqlBtn.addEventListener('click', async () => {
            const query = this.sqlQueryInput.value.trim();
            if (!query) {
                this.showDbStatus('请输入查询语句。', 'error');
                return;
            }

            this.showDbStatus('正在执行查询...', 'loading');
            this.dbResultsContainer.innerHTML = ''; // 清空之前的结果

            try {
                const response = await fetch('/api/db/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || `HTTP 错误! 状态: ${response.status}`);
                }

                this.renderDbResults(data);
                this.showDbStatus(`查询成功。找到 ${data.rows.length} 行数据。`, 'success');

            } catch (error) {
                console.error('数据库查询失败:', error);
                this.showDbStatus(`错误: ${error.message}`, 'error');
            }
        });
    }

    renderDbResults(data) {
        if (!data.rows || data.rows.length === 0) {
            this.dbResultsContainer.innerHTML = '<p class="no-results">查询未返回任何行。</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'db-results-table';

        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        data.headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);

        const tbody = document.createElement('tbody');
        data.rows.forEach(rowData => {
            const row = document.createElement('tr');
            rowData.forEach((cellData, columnIndex) => {
                const td = document.createElement('td');

                let formattedData = cellData;
                if (cellData !== null && cellData !== '') {
                    const columnName = data.headers[columnIndex];

                    // 特殊处理时间戳字段
                    if (columnName === 'timestamp_ms' && !isNaN(cellData)) {
                        const timestamp = Number(cellData);
                        if (timestamp > 0) {
                            const date = new Date(timestamp);
                            formattedData = date.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false
                            });
                        }
                    } else {
                        const num = Number(cellData);
                        // Check if the value is a valid finite number and it's a float (not an integer).
                        // This robustly handles values that are either actual numbers or strings representing numbers.
                        if (isFinite(num) && !Number.isInteger(num)) {
                            formattedData = num.toFixed(3);
                        }
                    }
                }

                td.textContent = formattedData === null ? 'NULL' : formattedData;
                row.appendChild(td);
            });
            tbody.appendChild(row);
        });
        table.appendChild(tbody);

        this.dbResultsContainer.innerHTML = ''; // 清空"加载中"或"无结果"的消息
        this.dbResultsContainer.appendChild(table);
    }

    showDbStatus(message, type) {
        this.dbStatusMessage.textContent = message;
        this.dbStatusMessage.className = 'status-message'; // 重置类
        if (type === 'error') {
            this.dbStatusMessage.classList.add('error');
        } else if (type === 'success') {
            this.dbStatusMessage.classList.add('success');
        }
    }

    setupControlHandlers() {
        if (!this.startBtn || !this.stopBtn || !this.refreshBtn) {
            console.error('主控制按钮未找到，无法绑定事件。');
            return;
        }

        this.startBtn.addEventListener('click', () => {
            console.log('[DEBUG] "启动系统"按钮被点击。正在发送START_SYSTEM命令...');
            this.sendSystemCommand('START_SYSTEM');

            // 恢复前端状态管理
            this.streamActive = true;
            this.startRenderLoop();
            this.updateStreamStatus('传输中', 'good');
            this.updateButtonStates();
        });

        this.stopBtn.addEventListener('click', () => {
            console.log('[DEBUG] "停止系统"按钮被点击。正在发送STOP_SYSTEM命令...');
            this.sendSystemCommand('STOP_SYSTEM');

            // 恢复前端状态管理
            this.streamActive = false;
            this.stopRenderLoop();
            this.updateStreamStatus('已停止', 'neutral');
            this.hideAllSignals();
            this.initializeFpsDisplay(); // 重置FPS显示
            this.updateButtonStates();
        });

        this.refreshBtn.addEventListener('click', () => {
            console.log('用户点击 [刷新]');
            window.location.reload();
        });
    }

    sendSystemCommand(command) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ command }));
            console.log(`已发送系统命令: ${command}`);
            this.showNotification(`已发送系统命令: ${command}`, 'info');
        } else {
            console.warn('无法发送系统命令，WebSocket未连接');
            this.showNotification('无法发送系统命令，WebSocket未连接', 'error');
        }
    }

    sendWebSocketMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
            console.log('📤 发送WebSocket消息:', message);
        } else {
            console.warn('无法发送WebSocket消息，连接未建立');
            this.showNotification('无法发送消息，WebSocket未连接', 'error');
        }
    }

    initTrajectoryVisualizer() {
        const visualizerContainer = document.getElementById('trajectory-visualizer-container');
        if (visualizerContainer) {
            console.log('🌌 初始化3D轨迹可视化器');
            this.trajectoryVisualizer = new TrajectoryVisualizer(visualizerContainer);
        } else {
            console.warn('⚠️ 3D轨迹可视化容器未找到，跳过初始化。');
        }
    }

    // ==================== 精彩录制功能相关方法 ====================

    setupHighlightRecordingHandlers() {
        console.log('⭐ 设置精彩录制控制处理器');

        // 检查DOM元素
        if (!this.highlightStartBtn || !this.highlightStopBtn) {
            console.error('❌ 精彩录制按钮未找到');
            return;
        }

        // 开始精彩录制按钮
        this.highlightStartBtn.addEventListener('click', () => {
            console.log('⭐ 开始精彩录制按钮被点击');
            this.startHighlightRecording();
        });

        // 停止精彩录制按钮
        this.highlightStopBtn.addEventListener('click', () => {
            console.log('⭐ 停止精彩录制按钮被点击');
            this.stopHighlightRecording();
        });

        // 球速阈值滑块
        if (this.speedThreshold && this.speedThresholdValue) {
            this.speedThreshold.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                this.speedThresholdValue.textContent = value.toFixed(1);
                this.currentSpeedThreshold = value;
                console.log('⭐ 球速阈值更新为:', value);
            });

            // 初始化显示值
            this.speedThresholdValue.textContent = this.speedThreshold.value;
            this.currentSpeedThreshold = parseFloat(this.speedThreshold.value);
        }

        // 启动定时器更新录制时长
        this.startHighlightDurationTimer();
    }

    // ==================== 检测框录制功能相关方法 ====================

    setupDetectionRecordingHandlers() {
        console.log('🎯 设置检测框录制控制处理器');

        // 检查DOM元素
        if (!this.detectionRecordingToggle || !this.recordingModeSelect) {
            console.error('❌ 检测框录制控制元素未找到');
            return;
        }

        // 检测框录制开关
        this.detectionRecordingToggle.addEventListener('change', (e) => {
            const enabled = e.target.checked;
            const mode = this.recordingModeSelect.value;
            console.log('🎯 检测框录制开关状态:', enabled, '模式:', mode);
            this.setDetectionRecording(enabled, mode);
        });

        // 录制模式选择
        this.recordingModeSelect.addEventListener('change', (e) => {
            const mode = e.target.value;
            const enabled = this.detectionRecordingToggle.checked;
            console.log('🎯 录制模式更改为:', mode, '启用状态:', enabled);
            this.setDetectionRecording(enabled, mode);
        });

        // 初始化状态
        this.loadDetectionRecordingStatus();
    }

    setDetectionRecording(enable, mode) {
        // 发送WebSocket消息
        this.sendWebSocketMessage({
            command: 'SET_DETECTION_RECORDING',
            enable: enable,
            mode: mode
        });

        // 更新状态显示
        this.updateDetectionRecordingStatus(enable, mode);
    }

    updateDetectionRecordingStatus(enabled, mode) {
        if (this.detectionRecordingStatus && this.detectionRecordingStatusText) {
            if (enabled) {
                this.detectionRecordingStatus.className = 'status-dot active';
                this.detectionRecordingStatusText.textContent = `已启用 (${mode === 'DETECTION_MODE' ? '带检测框' : '原始视频'})`;
            } else {
                this.detectionRecordingStatus.className = 'status-dot inactive';
                this.detectionRecordingStatusText.textContent = '未启用';
            }
        }
    }

    async loadDetectionRecordingStatus() {
        try {
            const response = await fetch('/api/recording/detection_status');
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'ok') {
                    // 更新UI状态
                    if (this.detectionRecordingToggle) {
                        this.detectionRecordingToggle.checked = data.enabled;
                    }
                    if (this.recordingModeSelect) {
                        this.recordingModeSelect.value = data.mode;
                    }
                    this.updateDetectionRecordingStatus(data.enabled, data.mode);
                }
            }
        } catch (error) {
            console.error('❌ 加载检测框录制状态失败:', error);
        }
    }

    handleDetectionRecordingStatus(message) {
        console.log('🎯 收到检测框录制状态更新:', message);

        if (message.enabled !== undefined && message.mode !== undefined) {
            // 更新UI状态
            if (this.detectionRecordingToggle) {
                this.detectionRecordingToggle.checked = message.enabled;
            }
            if (this.recordingModeSelect) {
                this.recordingModeSelect.value = message.mode;
            }
            this.updateDetectionRecordingStatus(message.enabled, message.mode);

            // 显示通知
            const modeText = message.mode === 'DETECTION_MODE' ? '带检测框' : '原始视频';
            const statusText = message.enabled ? '已启用' : '已禁用';
            this.showNotification(`检测框录制${statusText} (${modeText})`, 'success');
        }

        if (message.error) {
            this.showNotification(`检测框录制错误: ${message.error}`, 'error');
        }
    }

    startHighlightRecording() {
        if (this.highlightRecordingActive) {
            this.showNotification('精彩录制已在进行中', 'warning');
            return;
        }

        if (!this.streamActive) {
            this.showNotification('请先启动系统', 'error');
            return;
        }

        // 发送开始精彩录制命令
        this.sendWebSocketMessage({
            command: 'START_HIGHLIGHT_RECORDING',
            speed_threshold: this.currentSpeedThreshold,
            camera_id: 1  // 默认使用左摄像头
        });

        // 更新UI状态
        this.highlightRecordingActive = true;
        this.highlightRecordingStartTime = Date.now();
        this.highlightMomentsCount = 0;
        this.updateHighlightUI();

        this.showNotification(`开始精彩录制 (阈值: ${this.currentSpeedThreshold} m/s)`, 'success');
    }

    stopHighlightRecording() {
        if (!this.highlightRecordingActive) {
            this.showNotification('当前没有进行精彩录制', 'warning');
            return;
        }

        // 发送停止精彩录制命令
        this.sendWebSocketMessage({
            command: 'STOP_HIGHLIGHT_RECORDING'
        });

        // 更新UI状态
        this.highlightRecordingActive = false;
        this.highlightRecordingStartTime = null;
        this.updateHighlightUI();

        this.showNotification('正在生成精彩片段...', 'info');
    }

    updateHighlightUI() {
        if (!this.highlightStartBtn || !this.highlightStopBtn || !this.highlightStatus) {
            return;
        }

        if (this.highlightRecordingActive) {
            this.highlightStartBtn.style.display = 'none';
            this.highlightStopBtn.style.display = 'inline-block';
            this.highlightStatus.textContent = '录制中';
            this.highlightStatus.style.color = '#ff6b35';
        } else {
            this.highlightStartBtn.style.display = 'inline-block';
            this.highlightStopBtn.style.display = 'none';
            this.highlightStatus.textContent = '未开始';
            this.highlightStatus.style.color = '#ffd700';
        }

        // 更新精彩时刻计数
        if (this.highlightMoments) {
            this.highlightMoments.textContent = this.highlightMomentsCount;
        }
    }

    startHighlightDurationTimer() {
        setInterval(() => {
            if (this.highlightRecordingActive && this.highlightRecordingStartTime && this.highlightDuration) {
                const elapsed = Math.floor((Date.now() - this.highlightRecordingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                this.highlightDuration.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            } else if (this.highlightDuration && !this.highlightRecordingActive) {
                this.highlightDuration.textContent = '0s';
            }
        }, 1000);
    }

    handleHighlightMessage(message) {
        switch (message.type) {
            case 'highlight_status_update':
                if (message.recording_state !== undefined) {
                    this.highlightRecordingActive = message.recording_state === 'RECORDING';
                    this.updateHighlightUI();
                }
                if (message.moments_count !== undefined) {
                    this.highlightMomentsCount = message.moments_count;
                    this.updateHighlightUI();
                }
                if (message.error) {
                    this.showNotification(`精彩录制错误: ${message.error}`, 'error');
                }
                break;

            case 'highlight_completed':
                this.highlightRecordingActive = false;
                this.updateHighlightUI();
                if (message.clips_count > 0) {
                    this.showNotification(`精彩片段生成完成! 共生成 ${message.clips_count} 个片段`, 'success');
                } else {
                    this.showNotification('录制完成，但未检测到精彩时刻', 'info');
                }
                break;
        }
    }

    // ==================== 标定功能相关方法 ====================

    setupCalibrationHandlers() {
        console.log('🎯 设置标定控制处理器');

        // 调试：检查DOM元素是否找到
        console.log('calibrationBtn:', this.calibrationBtn);
        console.log('startCalibrationBtn:', this.startCalibrationBtn);
        console.log('stopCalibrationBtn:', this.stopCalibrationBtn);
        console.log('calibrationPanel:', this.calibrationPanel);

        // 主标定按钮 - 切换标定面板显示
        if (this.calibrationBtn) {
            console.log('✅ 主标定按钮找到，绑定事件');
            this.calibrationBtn.addEventListener('click', () => {
                console.log('🎯 主标定按钮被点击');
                this.toggleCalibrationPanel();
            });
        } else {
            console.error('❌ 主标定按钮未找到');
        }

        // 开始标定按钮
        if (this.startCalibrationBtn) {
            console.log('✅ 开始标定按钮找到，绑定事件');
            this.startCalibrationBtn.addEventListener('click', () => {
                console.log('🎯 开始标定按钮被点击');
                this.startCalibration();
            });
        } else {
            console.error('❌ 开始标定按钮未找到');
        }

        // 停止标定按钮
        if (this.stopCalibrationBtn) {
            console.log('✅ 停止标定按钮找到，绑定事件');
            this.stopCalibrationBtn.addEventListener('click', () => {
                console.log('🎯 停止标定按钮被点击');
                this.stopCalibration();
            });
        } else {
            console.error('❌ 停止标定按钮未找到');
        }

        // 初始化标定状态
        this.updateCalibrationStatus('idle', '空闲');
    }

    toggleCalibrationPanel() {
        console.log('🎯 toggleCalibrationPanel 被调用');
        console.log('calibrationPanel:', this.calibrationPanel);

        if (!this.calibrationPanel) {
            console.error('❌ calibrationPanel 未找到');
            return;
        }

        const isVisible = this.calibrationPanel.style.display !== 'none';
        console.log('当前面板可见状态:', isVisible);

        this.calibrationPanel.style.display = isVisible ? 'none' : 'block';
        console.log('设置面板显示状态为:', isVisible ? 'none' : 'block');

        if (!isVisible) {
            this.showNotification('标定面板已打开，请按照说明进行操作', 'info');
        }
    }

    startCalibration() {
        if (!this.connected) {
            this.showNotification('请先连接到系统', 'error');
            return;
        }

        if (this.calibrationActive) {
            this.showNotification('标定已在进行中', 'warning');
            return;
        }

        // 发送开始标定命令
        this.sendWebSocketMessage({
            type: 'start_calibration'
        });

        this.calibrationActive = true;
        this.updateCalibrationButtons(true);
        this.showCalibrationProgress(true);
        this.hideCalibrationResults();

        this.showNotification('开始相机标定，请确保标定板在摄像头视野内', 'info');
    }

    stopCalibration() {
        if (!this.calibrationActive) {
            return;
        }

        // 发送停止标定命令
        this.sendWebSocketMessage({
            type: 'stop_calibration'
        });

        this.calibrationActive = false;
        this.updateCalibrationButtons(false);
        this.showCalibrationProgress(false);
        this.updateCalibrationStatus('idle', '已停止');

        this.showNotification('标定已停止', 'info');
    }

    handleCalibrationStatus(message) {
        const { status, message: statusMessage, progress } = message;

        this.updateCalibrationStatus(status, statusMessage);

        if (progress !== undefined) {
            this.updateCalibrationProgress(progress, statusMessage);
        }

        // 根据状态更新UI
        switch (status) {
            case 'detecting_chessboard':
                this.updateCalibrationProgress(25, '正在检测标定板...');
                break;
            case 'calculating_parameters':
                this.updateCalibrationProgress(50, '正在计算标定参数...');
                break;
            case 'validating_results':
                this.updateCalibrationProgress(75, '正在验证标定结果...');
                break;
            case 'success':
                this.updateCalibrationProgress(100, '标定成功完成！');
                this.calibrationActive = false;
                this.updateCalibrationButtons(false);
                break;
            case 'failed_no_chessboard':
            case 'failed_validation':
            case 'failed_insufficient_points':
            case 'failed_file_write':
                this.calibrationActive = false;
                this.updateCalibrationButtons(false);
                this.showCalibrationProgress(false);
                this.showNotification(`标定失败: ${statusMessage}`, 'error');
                break;
        }
    }

    handleCalibrationResult(message) {
        const { status, mean_error, detected_corners, completion_time } = message;

        this.showCalibrationResults(true);

        if (this.calibrationResultStatus) {
            this.calibrationResultStatus.textContent = status === 'success' ? '成功' : '失败';
            this.calibrationResultStatus.style.color = status === 'success' ? 'var(--success-color)' : 'var(--error-color)';
        }

        if (this.calibrationResultError && mean_error !== undefined) {
            this.calibrationResultError.textContent = `${(mean_error * 1000).toFixed(2)} mm`;
        }

        if (this.calibrationResultCorners && detected_corners !== undefined) {
            this.calibrationResultCorners.textContent = `${detected_corners}/88`;
        }

        if (this.calibrationResultTime && completion_time) {
            this.calibrationResultTime.textContent = new Date(completion_time).toLocaleTimeString();
        }

        if (status === 'success') {
            this.showNotification(`标定成功完成！平均误差: ${(mean_error * 1000).toFixed(2)}mm`, 'success');
        }
    }

    updateCalibrationStatus(status, message) {
        // 更新状态卡片
        if (this.calibrationStatusCard) {
            // 找到状态卡片中的文本元素并更新
            const statusTextElement = this.calibrationStatusCard.querySelector('.status-text');
            if (statusTextElement) {
                statusTextElement.textContent = this.getCalibrationStatusText(status);
            }
        }

        // 更新状态指示器
        if (this.calibrationStatusText) {
            this.calibrationStatusText.textContent = message || this.getCalibrationStatusText(status);
        }

        if (this.calibrationStatusDot) {
            this.calibrationStatusDot.className = 'status-dot';
            switch (status) {
                case 'detecting_chessboard':
                case 'calculating_parameters':
                case 'validating_results':
                    this.calibrationStatusDot.classList.add('status-calibrating');
                    break;
                case 'success':
                    this.calibrationStatusDot.classList.add('status-success');
                    break;
                case 'failed_no_chessboard':
                case 'failed_validation':
                case 'failed_insufficient_points':
                case 'failed_file_write':
                    this.calibrationStatusDot.classList.add('status-error');
                    break;
                default:
                    // idle状态保持默认样式
                    break;
            }
        }

        // 更新状态卡片样式
        if (this.calibrationStatusCard) {
            this.calibrationStatusCard.className = 'status-card';
            this.calibrationStatusCard.id = 'calibration-status-card';

            switch (status) {
                case 'detecting_chessboard':
                case 'calculating_parameters':
                case 'validating_results':
                    this.calibrationStatusCard.classList.add('status-calibrating');
                    break;
                case 'success':
                    this.calibrationStatusCard.classList.add('status-success');
                    break;
                case 'failed_no_chessboard':
                case 'failed_validation':
                case 'failed_insufficient_points':
                case 'failed_file_write':
                    this.calibrationStatusCard.classList.add('status-error');
                    break;
            }
        }
    }

    getCalibrationStatusText(status) {
        const statusMap = {
            'idle': '空闲',
            'detecting_chessboard': '检测标定板',
            'calculating_parameters': '计算参数',
            'validating_results': '验证结果',
            'success': '成功',
            'failed_no_chessboard': '未检测到标定板',
            'failed_validation': '验证失败',
            'failed_insufficient_points': '角点数量不足',
            'failed_file_write': '文件写入失败'
        };
        return statusMap[status] || '未知状态';
    }

    updateCalibrationButtons(isCalibrating) {
        if (this.startCalibrationBtn) {
            this.startCalibrationBtn.style.display = isCalibrating ? 'none' : 'block';
            this.startCalibrationBtn.disabled = isCalibrating;
        }

        if (this.stopCalibrationBtn) {
            this.stopCalibrationBtn.style.display = isCalibrating ? 'block' : 'none';
            this.stopCalibrationBtn.disabled = !isCalibrating;
        }
    }

    showCalibrationProgress(show) {
        if (this.calibrationProgress) {
            this.calibrationProgress.style.display = show ? 'block' : 'none';
        }
    }

    updateCalibrationProgress(percentage, text) {
        if (this.calibrationProgressFill) {
            this.calibrationProgressFill.style.width = `${percentage}%`;
        }

        if (this.calibrationProgressText) {
            this.calibrationProgressText.textContent = text || `进度: ${percentage}%`;
        }
    }

    showCalibrationResults(show) {
        if (this.calibrationResults) {
            this.calibrationResults.style.display = show ? 'block' : 'none';
        }
    }

    hideCalibrationResults() {
        this.showCalibrationResults(false);
    }

    // ==================== 数据可视化功能 ====================

    initDataVisualization() {
        console.log('🎨 初始化数据可视化系统');

        // 设置事件监听器
        if (this.refreshChartsBtn) {
            this.refreshChartsBtn.addEventListener('click', () => this.refreshAllCharts());
        }

        if (this.exportDataBtn) {
            this.exportDataBtn.addEventListener('click', () => this.exportData());
        }

        if (this.timeRangeSelect) {
            this.timeRangeSelect.addEventListener('change', () => this.updateTimeSeriesChart());
        }

        // 筛选器事件监听器
        if (this.filterToggleBtn) {
            this.filterToggleBtn.addEventListener('click', () => this.toggleFilterPanel());
        }

        if (this.applyFiltersBtn) {
            this.applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        if (this.resetFiltersBtn) {
            this.resetFiltersBtn.addEventListener('click', () => this.resetFilters());
        }

        // 初始化图表
        this.initCharts();

        // 注释掉自动更新 - 交互式速度分析不需要自动更新
        // setInterval(() => this.updateCharts(), 5000);

        console.log('✅ 数据可视化系统初始化完成');
    }

    initCharts() {
        try {
            this.initSpeedDistributionChart();
            this.initSpeedTimeSeriesChart();
            this.initTrajectoryHeatmapChart();
            this.initMovementFrequencyChart();
            this.initSpeedEcgChart();
            this.initInteractiveChart();
            this.updateStatistics();
        } catch (error) {
            console.error('❌ 图表初始化失败:', error);
        }
    }

    initSpeedDistributionChart() {
        const canvas = document.getElementById('speedDistributionChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.speedDistribution = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['0-5', '5-10', '10-15', '15-20', '20-25', '25+'],
                datasets: [{
                    label: '球速分布 (m/s)',
                    data: [0, 0, 0, 0, 0, 0],
                    backgroundColor: 'rgba(0, 212, 255, 0.6)',
                    borderColor: 'rgba(0, 212, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(0, 212, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(0, 212, 255, 0.1)'
                        }
                    }
                }
            }
        });

        this.updateChartStatus('speedChartStatus', '已初始化');
    }

    initSpeedTimeSeriesChart() {
        const canvas = document.getElementById('speedTimeSeriesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.speedTimeSeries = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '球速 (m/s)',
                    data: [],
                    borderColor: 'rgba(0, 255, 170, 1)',
                    backgroundColor: 'rgba(0, 255, 170, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(0, 255, 170, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#a0a0a0',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(0, 255, 170, 0.1)'
                        }
                    }
                }
            }
        });
    }

    initTrajectoryHeatmapChart() {
        const canvas = document.getElementById('trajectoryHeatmapChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // 创建热力图散点图
        this.charts.trajectoryHeatmap = new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: '运动热力图',
                    data: [],
                    backgroundColor: function(context) {
                        const point = context.parsed;
                        if (point && point.density !== undefined) {
                            const density = point.density;
                            // 根据密度生成颜色：蓝色(低) -> 绿色(中) -> 红色(高)
                            if (density < 0.3) {
                                return `rgba(0, 100, 255, ${0.4 + density * 0.6})`;
                            } else if (density < 0.7) {
                                return `rgba(0, 255, 100, ${0.4 + density * 0.6})`;
                            } else {
                                return `rgba(255, 50, 0, ${0.4 + density * 0.6})`;
                            }
                        }
                        return 'rgba(255, 107, 53, 0.6)';
                    },
                    borderColor: function(context) {
                        const point = context.parsed;
                        if (point && point.density !== undefined) {
                            const density = point.density;
                            if (density < 0.3) {
                                return 'rgba(0, 100, 255, 1)';
                            } else if (density < 0.7) {
                                return 'rgba(0, 255, 100, 1)';
                            } else {
                                return 'rgba(255, 50, 0, 1)';
                            }
                        }
                        return 'rgba(255, 107, 53, 1)';
                    },
                    pointRadius: function(context) {
                        const point = context.parsed;
                        if (point && point.density !== undefined) {
                            // 根据密度调整点的大小
                            return 3 + point.density * 8; // 3-11像素
                        }
                        return 5;
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.raw;
                                if (point && point.count !== undefined) {
                                    return [
                                        `坐标: (${point.x.toFixed(2)}, ${point.y.toFixed(2)})`,
                                        `密度: ${(point.density * 100).toFixed(1)}%`,
                                        `点数: ${point.count}`
                                    ];
                                }
                                return `坐标: (${context.parsed.x.toFixed(2)}, ${context.parsed.y.toFixed(2)})`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: 'Y 坐标 (m)',
                            color: '#a0a0a0'
                        },
                        min: 0,
                        max: 1.525, // 球桌宽度
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(255, 107, 53, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'X 坐标 (m)',
                            color: '#a0a0a0'
                        },
                        min: 0,
                        max: 2.74, // 球桌长度
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(255, 107, 53, 0.1)'
                        }
                    }
                }
            }
        });

        this.updateChartStatus('heatmapStatus', '已初始化');
    }

    updateChartStatus(statusId, message) {
        const statusElement = document.getElementById(statusId);
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    async refreshAllCharts() {
        console.log('🔄 刷新所有图表');

        // 开始性能监控
        this.startPerformanceMonitoring();

        // 检查数据连接
        const isConnected = await this.checkDataConnection();
        if (!isConnected) {
            this.showNotification('数据连接失败，请检查服务器状态', 'error');
            return;
        }

        // 显示加载状态
        this.setLoadingState(true);
        this.updateChartStatus('speedChartStatus', '更新中...');
        this.updateChartStatus('heatmapStatus', '更新中...');
        this.updateChartStatus('statsStatus', '计算中...');
        this.updateChartStatus('frequencyStatus', '分析中...');
        this.updateChartStatus('ecgStatus', '监测中...');

        try {
            // 并行更新图表以提高性能
            await Promise.all([
                this.updateCharts(),
                this.updateStatistics()
            ]);

            console.log('✅ 图表刷新完成');
            this.showNotification('图表更新完成', 'success');
        } catch (error) {
            console.error('❌ 图表刷新失败:', error);
            this.updateChartStatus('speedChartStatus', '更新失败');
            this.updateChartStatus('heatmapStatus', '更新失败');
            this.updateChartStatus('statsStatus', '计算失败');
            this.updateChartStatus('frequencyStatus', '分析失败');
            this.updateChartStatus('ecgStatus', '监测失败');
            this.showNotification('图表更新失败，请检查数据连接', 'error');
        } finally {
            // 隐藏加载状态
            this.setLoadingState(false);

            // 结束性能监控
            this.endPerformanceMonitoring();
        }
    }

    setLoadingState(isLoading) {
        // 更新刷新按钮状态
        if (this.refreshChartsBtn) {
            this.refreshChartsBtn.disabled = isLoading;
            const icon = this.refreshChartsBtn.querySelector('i');
            if (icon) {
                if (isLoading) {
                    icon.className = 'fas fa-spinner fa-spin';
                } else {
                    icon.className = 'fas fa-sync-alt';
                }
            }
        }

        // 更新导出按钮状态
        if (this.exportDataBtn) {
            this.exportDataBtn.disabled = isLoading;
        }

        // 更新筛选按钮状态
        if (this.applyFiltersBtn) {
            this.applyFiltersBtn.disabled = isLoading;
        }
    }

    async updateCharts() {
        try {
            // 并行更新图表以提高性能
            const updatePromises = [
                this.updateSpeedDistributionChart(),
                this.updateTimeSeriesChart(),
                this.updateTrajectoryHeatmapChart(),
                this.updateMovementFrequencyChart(),
                this.updateSpeedEcgChart()
                // 移除交互式图表的自动更新
                // this.updateInteractiveChart()
            ];

            // 使用Promise.allSettled确保即使某个图表更新失败，其他图表仍能正常更新
            const results = await Promise.allSettled(updatePromises);

            // 检查是否有失败的更新
            const failedUpdates = results.filter(result => result.status === 'rejected');
            if (failedUpdates.length > 0) {
                console.warn(`⚠️ ${failedUpdates.length} 个图表更新失败`);
                failedUpdates.forEach((result, index) => {
                    console.error(`图表 ${index + 1} 更新失败:`, result.reason);
                });
            }
        } catch (error) {
            console.error('❌ 更新图表失败:', error);
            throw error;
        }
    }

    // 数据采样函数，用于处理大数据量
    sampleData(data, maxPoints = 1000) {
        if (!data || data.length <= maxPoints) {
            return data;
        }

        const step = Math.ceil(data.length / maxPoints);
        const sampledData = [];

        for (let i = 0; i < data.length; i += step) {
            sampledData.push(data[i]);
        }

        console.log(`📊 数据采样: ${data.length} -> ${sampledData.length} 个数据点`);
        return sampledData;
    }

    async updateSpeedDistributionChart() {
        if (!this.charts.speedDistribution) return;

        try {
            // 查询最近的速度数据
            let query = `
                SELECT speed FROM trajectory
                WHERE speed > 0
                ORDER BY timestamp_ms DESC
                LIMIT 1000
            `;

            // 应用筛选条件
            query = this.buildFilteredQuery(query);

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 统计速度分布
            const bins = [0, 0, 0, 0, 0, 0]; // 0-5, 5-10, 10-15, 15-20, 20-25, 25+

            data.rows.forEach(row => {
                const speed = parseFloat(row[0]);
                if (speed < 5) bins[0]++;
                else if (speed < 10) bins[1]++;
                else if (speed < 15) bins[2]++;
                else if (speed < 20) bins[3]++;
                else if (speed < 25) bins[4]++;
                else bins[5]++;
            });

            this.charts.speedDistribution.data.datasets[0].data = bins;
            this.charts.speedDistribution.update();

            this.updateChartStatus('speedChartStatus', `已更新 (${data.rows.length} 个数据点)`);

        } catch (error) {
            console.error('❌ 更新速度分布图失败:', error);
            this.updateChartStatus('speedChartStatus', `更新失败: ${this.getErrorMessage(error)}`);
            this.handleChartError('speedDistribution', error);
        }
    }

    async updateTimeSeriesChart() {
        if (!this.charts.speedTimeSeries) return;

        try {
            const timeRange = parseInt(this.timeRangeSelect?.value || '300'); // 默认5分钟
            const currentTime = Date.now();
            const startTime = currentTime - (timeRange * 1000);

            let query = `
                SELECT timestamp_ms, speed FROM trajectory
                WHERE timestamp_ms > ${startTime} AND speed > 0
                ORDER BY timestamp_ms ASC
            `;

            // 应用筛选条件
            query = this.buildFilteredQuery(query);

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 处理时间序列数据
            const labels = [];
            const speeds = [];

            data.rows.forEach(row => {
                const timestamp = parseInt(row[0]);
                const speed = parseFloat(row[1]);

                const date = new Date(timestamp);
                labels.push(date.toLocaleTimeString());
                speeds.push(speed);
            });

            // 如果数据点太多，进行采样
            const maxPoints = 50;
            if (labels.length > maxPoints) {
                const step = Math.floor(labels.length / maxPoints);
                const sampledLabels = [];
                const sampledSpeeds = [];

                for (let i = 0; i < labels.length; i += step) {
                    sampledLabels.push(labels[i]);
                    sampledSpeeds.push(speeds[i]);
                }

                this.charts.speedTimeSeries.data.labels = sampledLabels;
                this.charts.speedTimeSeries.data.datasets[0].data = sampledSpeeds;
            } else {
                this.charts.speedTimeSeries.data.labels = labels;
                this.charts.speedTimeSeries.data.datasets[0].data = speeds;
            }

            this.charts.speedTimeSeries.update();

        } catch (error) {
            console.error('❌ 更新时间序列图失败:', error);
        }
    }

    async updateTrajectoryHeatmapChart() {
        if (!this.charts.trajectoryHeatmap) return;

        try {
            let query = `
                SELECT pos_x, pos_y FROM trajectory
                WHERE pos_x IS NOT NULL AND pos_y IS NOT NULL
                ORDER BY timestamp_ms DESC
                LIMIT 1000
            `;

            // 应用筛选条件
            query = this.buildFilteredQuery(query);

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 创建热力图数据
            const heatmapData = this.generateHeatmapData(data.rows);

            this.charts.trajectoryHeatmap.data.datasets[0].data = heatmapData;
            this.charts.trajectoryHeatmap.update();

            this.updateChartStatus('heatmapStatus', `已更新 (${data.rows.length} 个轨迹点)`);

        } catch (error) {
            console.error('❌ 更新轨迹热力图失败:', error);
            this.updateChartStatus('heatmapStatus', '更新失败');
        }
    }

    generateHeatmapData(rawData) {
        // 创建网格来统计密度
        const gridSize = 20; // 20x20 网格
        const tableWidth = 2.74; // 球桌长度 (m)
        const tableHeight = 1.525; // 球桌宽度 (m)

        const cellWidth = tableWidth / gridSize;
        const cellHeight = tableHeight / gridSize;

        // 初始化网格
        const grid = Array(gridSize).fill().map(() => Array(gridSize).fill(0));

        // 统计每个网格的点数
        rawData.forEach(row => {
            const x = parseFloat(row[0]);
            const y = parseFloat(row[1]);

            if (x >= 0 && x <= tableWidth && y >= 0 && y <= tableHeight) {
                const gridX = Math.min(Math.floor(x / cellWidth), gridSize - 1);
                const gridY = Math.min(Math.floor(y / cellHeight), gridSize - 1);
                grid[gridY][gridX]++;
            }
        });

        // 找到最大密度用于归一化
        let maxDensity = 0;
        grid.forEach(row => {
            row.forEach(cell => {
                maxDensity = Math.max(maxDensity, cell);
            });
        });

        // 转换为散点图数据，颜色和大小表示密度
        const heatmapData = [];
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const density = grid[i][j];
                if (density > 0) {
                    const normalizedDensity = density / maxDensity;
                    const x = (j + 0.5) * cellWidth;
                    const y = (i + 0.5) * cellHeight;

                    heatmapData.push({
                        x: x,
                        y: y,
                        density: normalizedDensity,
                        count: density
                    });
                }
            }
        }

        return heatmapData;
    }

    async updateStatistics() {
        try {
            // 查询统计数据
            const queries = {
                avgSpeed: "SELECT AVG(speed) FROM trajectory WHERE speed > 0",
                maxSpeed: "SELECT MAX(speed) FROM trajectory WHERE speed > 0",
                dataCount: "SELECT COUNT(*) FROM trajectory",
                timeRange: "SELECT MIN(timestamp_ms), MAX(timestamp_ms) FROM trajectory"
            };

            const results = {};

            for (const [key, query] of Object.entries(queries)) {
                try {
                    const response = await fetch('/api/db/query', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results[key] = data.rows[0] ? data.rows[0][0] : null;
                    }
                } catch (error) {
                    console.error(`❌ 查询${key}失败:`, error);
                    results[key] = null;
                }
            }

            // 更新统计显示
            if (this.avgSpeed) {
                const avg = results.avgSpeed ? parseFloat(results.avgSpeed).toFixed(2) : '--';
                this.avgSpeed.textContent = `${avg} m/s`;
            }

            if (this.maxSpeed) {
                const max = results.maxSpeed ? parseFloat(results.maxSpeed).toFixed(2) : '--';
                this.maxSpeed.textContent = `${max} m/s`;
            }

            if (this.dataPointCount) {
                const count = results.dataCount ? parseInt(results.dataCount).toLocaleString() : '--';
                this.dataPointCount.textContent = count;
            }

            if (this.activeDuration && results.timeRange) {
                const minTime = parseInt(results.timeRange);
                const maxTime = parseInt(results.maxSpeed); // 这里应该是查询结果的第二个值
                if (minTime && maxTime) {
                    const duration = Math.round((maxTime - minTime) / 60000); // 转换为分钟
                    this.activeDuration.textContent = `${duration} 分钟`;
                } else {
                    this.activeDuration.textContent = '-- 分钟';
                }
            }

            this.updateChartStatus('statsStatus', '已更新');

        } catch (error) {
            console.error('❌ 更新统计数据失败:', error);
            this.updateChartStatus('statsStatus', '更新失败');
        }
    }

    async exportData() {
        try {
            console.log('📥 开始导出数据');

            const query = `
                SELECT id, timestamp_ms, camera_id, pos_x, pos_y, pos_z, speed
                FROM trajectory
                ORDER BY timestamp_ms DESC
                LIMIT 10000
            `;

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 转换为CSV格式
            const headers = data.headers.join(',');
            const rows = data.rows.map(row => row.join(',')).join('\n');
            const csvContent = `${headers}\n${rows}`;

            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `trajectory_data_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showNotification(`数据导出成功！共 ${data.rows.length} 条记录`, 'success');

        } catch (error) {
            console.error('❌ 数据导出失败:', error);
            this.showNotification('数据导出失败', 'error');
        }
    }

    initMovementFrequencyChart() {
        const canvas = document.getElementById('movementFrequencyChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.movementFrequency = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '运动频率 (次/分钟)',
                    data: [],
                    borderColor: 'rgba(255, 206, 84, 1)',
                    backgroundColor: 'rgba(255, 206, 84, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '运动频率 (次/分钟)',
                            color: '#a0a0a0'
                        },
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(255, 206, 84, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间',
                            color: '#a0a0a0'
                        },
                        ticks: {
                            color: '#a0a0a0',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(255, 206, 84, 0.1)'
                        }
                    }
                }
            }
        });

        this.updateChartStatus('frequencyStatus', '已初始化');
    }

    initSpeedEcgChart() {
        const canvas = document.getElementById('speedEcgChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // 初始化心电图数据缓存
        this.ecgData = {
            timeLabels: [],
            speedValues: [],
            maxDataPoints: 100, // 显示最近100个数据点
            currentSpeed: 0
        };

        // 创建心电图样式的图表
        this.charts.speedEcg = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.ecgData.timeLabels,
                datasets: [{
                    label: '球速 (m/s)',
                    data: this.ecgData.speedValues,
                    borderColor: '#00ff00', // 心电图绿色
                    backgroundColor: 'rgba(0, 255, 0, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.1, // 轻微平滑
                    pointRadius: 0, // 不显示数据点
                    pointHoverRadius: 4,
                    pointBackgroundColor: '#00ff00',
                    pointBorderColor: '#00ff00',
                    pointBorderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 0 // 禁用动画以获得实时效果
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false // 隐藏图例
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#00ff00',
                        bodyColor: '#ffffff',
                        borderColor: '#00ff00',
                        borderWidth: 1
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 30, // 设置最大值为30 m/s
                        title: {
                            display: true,
                            text: '球速 (m/s)',
                            color: '#00ff00',
                            font: {
                                family: 'Orbitron',
                                size: 12
                            }
                        },
                        ticks: {
                            color: '#00ff00',
                            font: {
                                family: 'Orbitron',
                                size: 10
                            }
                        },
                        grid: {
                            color: 'rgba(0, 255, 0, 0.2)',
                            lineWidth: 1
                        }
                    },
                    x: {
                        display: false, // 隐藏X轴标签，营造心电图效果
                        grid: {
                            color: 'rgba(0, 255, 0, 0.1)',
                            lineWidth: 1
                        }
                    }
                }
            }
        });

        this.updateChartStatus('ecgStatus', '已初始化');
        console.log('💚 球速心电图初始化完成');
    }

    async updateMovementFrequencyChart() {
        if (!this.charts.movementFrequency) return;

        try {
            // 查询最近30分钟的数据，按分钟统计运动频率
            const currentTime = Date.now();
            const startTime = currentTime - (30 * 60 * 1000); // 30分钟前

            let query = `
                SELECT
                    (timestamp_ms / 60000) * 60000 as minute_timestamp,
                    COUNT(*) as movement_count
                FROM trajectory
                WHERE timestamp_ms > ${startTime} AND speed > 1.0
                GROUP BY (timestamp_ms / 60000)
                ORDER BY minute_timestamp ASC
            `;

            // 应用筛选条件
            query = this.buildFilteredQuery(query);

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 处理频率数据
            const labels = [];
            const frequencies = [];

            data.rows.forEach(row => {
                const timestamp = parseInt(row[0]);
                const count = parseInt(row[1]);

                const date = new Date(timestamp);
                labels.push(date.toLocaleTimeString().slice(0, 5)); // HH:MM
                frequencies.push(count);
            });

            this.charts.movementFrequency.data.labels = labels;
            this.charts.movementFrequency.data.datasets[0].data = frequencies;
            this.charts.movementFrequency.update();

            this.updateChartStatus('frequencyStatus', `已更新 (${data.rows.length} 个时间点)`);

        } catch (error) {
            console.error('❌ 更新运动频率图失败:', error);
            this.updateChartStatus('frequencyStatus', '更新失败');
        }
    }

    async updateSpeedEcgChart() {
        if (!this.charts.speedEcg) return;

        try {
            // 查询最近30秒的速度数据，用于心电图显示
            const currentTime = Date.now();
            const startTime = currentTime - (30 * 1000); // 30秒前

            let query = `
                SELECT timestamp_ms, speed FROM trajectory
                WHERE timestamp_ms > ${startTime} AND speed > 0
                ORDER BY timestamp_ms ASC
            `;

            // 应用筛选条件
            query = this.buildFilteredQuery(query);

            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 处理心电图数据
            const newTimeLabels = [];
            const newSpeedValues = [];

            data.rows.forEach(row => {
                const timestamp = parseInt(row[0]);
                const speed = parseFloat(row[1]);

                // 使用相对时间（秒）作为X轴
                const relativeTime = (timestamp - startTime) / 1000;
                newTimeLabels.push(relativeTime.toFixed(1));
                newSpeedValues.push(speed);
            });

            // 更新心电图数据
            this.ecgData.timeLabels = newTimeLabels;
            this.ecgData.speedValues = newSpeedValues;

            // 获取当前最新的球速
            if (newSpeedValues.length > 0) {
                this.ecgData.currentSpeed = newSpeedValues[newSpeedValues.length - 1];
                this.updateCurrentSpeedDisplay(this.ecgData.currentSpeed);
            }

            // 更新图表
            this.charts.speedEcg.data.labels = this.ecgData.timeLabels;
            this.charts.speedEcg.data.datasets[0].data = this.ecgData.speedValues;
            this.charts.speedEcg.update('none'); // 使用'none'模式避免动画

            this.updateChartStatus('ecgStatus', `监测中 (${data.rows.length} 个数据点)`);

        } catch (error) {
            console.error('❌ 更新球速心电图失败:', error);
            this.updateChartStatus('ecgStatus', '监测失败');
        }
    }

    updateCurrentSpeedDisplay(speed) {
        const speedElement = document.getElementById('ecgCurrentSpeed');
        if (speedElement) {
            speedElement.textContent = speed.toFixed(1);

            // 根据速度值改变颜色
            if (speed > 20) {
                speedElement.style.color = '#ff4444'; // 高速红色
                speedElement.style.textShadow = '0 0 6px rgba(255, 68, 68, 0.8)';
            } else if (speed > 10) {
                speedElement.style.color = '#ffaa00'; // 中速橙色
                speedElement.style.textShadow = '0 0 6px rgba(255, 170, 0, 0.8)';
            } else {
                speedElement.style.color = '#00ff00'; // 低速绿色
                speedElement.style.textShadow = '0 0 4px rgba(0, 255, 0, 0.5)';
            }
        }
    }

    // ==================== 数据筛选功能 ====================

    toggleFilterPanel() {
        const panel = this.dataFilterPanel;
        if (panel) {
            const isVisible = panel.style.display !== 'none';
            panel.style.display = isVisible ? 'none' : 'block';

            // 更新按钮文本
            if (this.filterToggleBtn) {
                const icon = this.filterToggleBtn.querySelector('i');
                if (icon) {
                    icon.className = isVisible ? 'fas fa-filter' : 'fas fa-times';
                }
            }

            console.log(`🔍 筛选器面板${isVisible ? '隐藏' : '显示'}`);
        }
    }

    applyFilters() {
        console.log('🔍 应用数据筛选器');

        // 获取筛选条件
        const filters = this.getFilterConditions();

        if (Object.keys(filters).length === 0) {
            this.showNotification('未设置任何筛选条件', 'warning');
            return;
        }

        // 应用筛选并更新图表
        this.currentFilters = filters;
        this.refreshAllCharts();

        this.showNotification('筛选条件已应用', 'success');
    }

    resetFilters() {
        console.log('🔍 重置数据筛选器');

        // 清空所有筛选输入
        if (this.startTimeFilter) this.startTimeFilter.value = '';
        if (this.endTimeFilter) this.endTimeFilter.value = '';
        if (this.minSpeedFilter) this.minSpeedFilter.value = '';
        if (this.maxSpeedFilter) this.maxSpeedFilter.value = '';
        if (this.cameraFilter) this.cameraFilter.value = '';
        if (this.minXFilter) this.minXFilter.value = '';
        if (this.maxXFilter) this.maxXFilter.value = '';
        if (this.minYFilter) this.minYFilter.value = '';
        if (this.maxYFilter) this.maxYFilter.value = '';
        if (this.minZFilter) this.minZFilter.value = '';
        if (this.maxZFilter) this.maxZFilter.value = '';

        // 清除当前筛选条件
        this.currentFilters = {};

        // 刷新图表
        this.refreshAllCharts();

        this.showNotification('筛选条件已重置', 'success');
    }

    getFilterConditions() {
        const filters = {};

        // 时间范围筛选
        if (this.startTimeFilter && this.startTimeFilter.value) {
            filters.startTime = new Date(this.startTimeFilter.value).getTime();
        }
        if (this.endTimeFilter && this.endTimeFilter.value) {
            filters.endTime = new Date(this.endTimeFilter.value).getTime();
        }

        // 速度范围筛选
        if (this.minSpeedFilter && this.minSpeedFilter.value) {
            filters.minSpeed = parseFloat(this.minSpeedFilter.value);
        }
        if (this.maxSpeedFilter && this.maxSpeedFilter.value) {
            filters.maxSpeed = parseFloat(this.maxSpeedFilter.value);
        }

        // 摄像头筛选
        if (this.cameraFilter && this.cameraFilter.value) {
            filters.cameraId = parseInt(this.cameraFilter.value);
        }

        // 位置范围筛选
        if (this.minXFilter && this.minXFilter.value) {
            filters.minX = parseFloat(this.minXFilter.value);
        }
        if (this.maxXFilter && this.maxXFilter.value) {
            filters.maxX = parseFloat(this.maxXFilter.value);
        }
        if (this.minYFilter && this.minYFilter.value) {
            filters.minY = parseFloat(this.minYFilter.value);
        }
        if (this.maxYFilter && this.maxYFilter.value) {
            filters.maxY = parseFloat(this.maxYFilter.value);
        }
        if (this.minZFilter && this.minZFilter.value) {
            filters.minZ = parseFloat(this.minZFilter.value);
        }
        if (this.maxZFilter && this.maxZFilter.value) {
            filters.maxZ = parseFloat(this.maxZFilter.value);
        }

        return filters;
    }

    buildFilteredQuery(baseQuery) {
        if (!this.currentFilters || Object.keys(this.currentFilters).length === 0) {
            return baseQuery;
        }

        const conditions = [];

        // 时间范围条件
        if (this.currentFilters.startTime) {
            conditions.push(`timestamp_ms >= ${this.currentFilters.startTime}`);
        }
        if (this.currentFilters.endTime) {
            conditions.push(`timestamp_ms <= ${this.currentFilters.endTime}`);
        }

        // 速度范围条件
        if (this.currentFilters.minSpeed !== undefined) {
            conditions.push(`speed >= ${this.currentFilters.minSpeed}`);
        }
        if (this.currentFilters.maxSpeed !== undefined) {
            conditions.push(`speed <= ${this.currentFilters.maxSpeed}`);
        }

        // 摄像头条件
        if (this.currentFilters.cameraId !== undefined) {
            conditions.push(`camera_id = ${this.currentFilters.cameraId}`);
        }

        // 位置范围条件
        if (this.currentFilters.minX !== undefined) {
            conditions.push(`pos_x >= ${this.currentFilters.minX}`);
        }
        if (this.currentFilters.maxX !== undefined) {
            conditions.push(`pos_x <= ${this.currentFilters.maxX}`);
        }
        if (this.currentFilters.minY !== undefined) {
            conditions.push(`pos_y >= ${this.currentFilters.minY}`);
        }
        if (this.currentFilters.maxY !== undefined) {
            conditions.push(`pos_y <= ${this.currentFilters.maxY}`);
        }
        if (this.currentFilters.minZ !== undefined) {
            conditions.push(`pos_z >= ${this.currentFilters.minZ}`);
        }
        if (this.currentFilters.maxZ !== undefined) {
            conditions.push(`pos_z <= ${this.currentFilters.maxZ}`);
        }

        // 将条件添加到查询中
        if (conditions.length > 0) {
            const whereClause = conditions.join(' AND ');

            // 检查查询是否已经有WHERE子句
            if (baseQuery.toLowerCase().includes('where')) {
                return baseQuery + ' AND ' + whereClause;
            } else {
                // 在ORDER BY之前插入WHERE子句
                const orderByIndex = baseQuery.toLowerCase().indexOf('order by');
                if (orderByIndex !== -1) {
                    return baseQuery.substring(0, orderByIndex) + 'WHERE ' + whereClause + ' ' + baseQuery.substring(orderByIndex);
                } else {
                    return baseQuery + ' WHERE ' + whereClause;
                }
            }
        }

        return baseQuery;
    }

    // ==================== 交互式速度-时间图表组件 ====================

    initInteractiveChart() {
        try {
            console.log('🚀 初始化交互式速度-时间图表组件');

            // 创建交互式图表实例
            this.interactiveChart = new SpeedTimeInteractiveChart('speedTimeInteractiveChart', {
                maxDataPoints: 1000,
                timeRange: 300,
                leftCameraColor: 'rgba(54, 162, 235, 1)',
                rightCameraColor: 'rgba(255, 99, 132, 1)'
            });

            // 绑定控制按钮事件
            this.setupInteractiveChartControls();

            // 初始化数据更新
            this.updateInteractiveChart();

            console.log('✅ 交互式速度-时间图表组件初始化完成');
        } catch (error) {
            console.error('❌ 交互式图表初始化失败:', error);
            this.updateChartStatus('interactiveChartStatus', '初始化失败');
        }
    }

    setupInteractiveChartControls() {
        // 缩放按钮
        if (this.zoomInBtn) {
            this.zoomInBtn.addEventListener('click', () => {
                if (this.interactiveChart) {
                    const oldZoom = this.interactiveChart.zoomLevel;
                    this.interactiveChart.zoomLevel *= 1.2;
                    this.interactiveChart.zoomLevel = Math.min(10, this.interactiveChart.zoomLevel);
                    console.log(`🔍+ 放大按钮: ${oldZoom.toFixed(2)} -> ${this.interactiveChart.zoomLevel.toFixed(2)}`);
                    this.interactiveChart.updateChartZoom();
                }
            });
        }

        if (this.zoomOutBtn) {
            this.zoomOutBtn.addEventListener('click', () => {
                if (this.interactiveChart) {
                    const oldZoom = this.interactiveChart.zoomLevel;
                    this.interactiveChart.zoomLevel *= 0.8;
                    this.interactiveChart.zoomLevel = Math.max(0.1, this.interactiveChart.zoomLevel);
                    console.log(`🔍- 缩小按钮: ${oldZoom.toFixed(2)} -> ${this.interactiveChart.zoomLevel.toFixed(2)}`);
                    this.interactiveChart.updateChartZoom();
                }
            });
        }

        if (this.resetViewBtn) {
            this.resetViewBtn.addEventListener('click', () => {
                if (this.interactiveChart) {
                    this.interactiveChart.resetView();
                }
            });
        }

        // 手动刷新按钮
        if (this.refreshInteractiveBtn) {
            this.refreshInteractiveBtn.addEventListener('click', () => {
                console.log('🔄 手动刷新交互式速度分析数据');
                this.updateInteractiveChart();
            });
        }

        // 摄像头切换
        if (this.leftCameraToggle) {
            this.leftCameraToggle.addEventListener('change', (e) => {
                if (this.interactiveChart) {
                    this.interactiveChart.showLeftCamera = e.target.checked;
                    this.interactiveChart.toggleDataset(0);
                }
            });
        }

        if (this.rightCameraToggle) {
            this.rightCameraToggle.addEventListener('change', (e) => {
                if (this.interactiveChart) {
                    this.interactiveChart.showRightCamera = e.target.checked;
                    this.interactiveChart.toggleDataset(1);
                }
            });
        }
    }

    async updateInteractiveChart() {
        if (!this.interactiveChart) return;

        try {
            this.updateChartStatus('interactiveChartStatus', '更新中...');
            await this.interactiveChart.updateData();
            this.updateChartStatus('interactiveChartStatus', '已更新');
        } catch (error) {
            console.error('❌ 更新交互式图表失败:', error);
            this.updateChartStatus('interactiveChartStatus', '更新失败');
        }
    }

    // ==================== 错误处理和性能优化 ====================

    getErrorMessage(error) {
        if (error.message) {
            return error.message;
        } else if (typeof error === 'string') {
            return error;
        } else {
            return '未知错误';
        }
    }

    handleChartError(chartName, error) {
        console.error(`📊 图表 ${chartName} 错误:`, error);

        // 记录错误统计
        if (!this.errorStats) {
            this.errorStats = {};
        }
        if (!this.errorStats[chartName]) {
            this.errorStats[chartName] = 0;
        }
        this.errorStats[chartName]++;

        // 如果错误次数过多，暂停自动更新
        if (this.errorStats[chartName] >= 3) {
            console.warn(`⚠️ 图表 ${chartName} 错误次数过多，暂停自动更新`);
            this.showNotification(`图表 ${chartName} 暂停更新，请检查数据源`, 'warning');
        }
    }

    // 防抖函数，用于优化频繁的更新操作
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数，用于限制更新频率
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 检查数据连接状态
    async checkDataConnection() {
        try {
            const response = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query: 'SELECT 1' })
            });

            return response.ok;
        } catch (error) {
            console.error('❌ 数据连接检查失败:', error);
            return false;
        }
    }

    // 性能监控
    startPerformanceMonitoring() {
        if (!this.performanceMetrics) {
            this.performanceMetrics = {
                updateCount: 0,
                totalUpdateTime: 0,
                lastUpdateTime: 0
            };
        }

        this.performanceMetrics.lastUpdateTime = performance.now();
    }

    endPerformanceMonitoring() {
        if (this.performanceMetrics && this.performanceMetrics.lastUpdateTime) {
            const updateTime = performance.now() - this.performanceMetrics.lastUpdateTime;
            this.performanceMetrics.updateCount++;
            this.performanceMetrics.totalUpdateTime += updateTime;

            const avgUpdateTime = this.performanceMetrics.totalUpdateTime / this.performanceMetrics.updateCount;

            console.log(`⏱️ 图表更新耗时: ${updateTime.toFixed(2)}ms, 平均: ${avgUpdateTime.toFixed(2)}ms`);

            // 如果更新时间过长，给出警告
            if (updateTime > 5000) { // 5秒
                console.warn('⚠️ 图表更新时间过长，建议优化数据查询或减少数据量');
                this.showNotification('图表更新较慢，正在优化...', 'warning');
            }
        }
    }
}

/**
 * 3D Trajectory Visualizer using Three.js
 */
class TrajectoryVisualizer {
    constructor(container) {
        this.container = container;

        // --- 场景常量 (用户坐标系：X-宽度, Y-长度, Z-高度, 原点在左前角) ---
        this.TABLE_LENGTH = 2.74;  // Y轴: 球桌长度
        this.TABLE_WIDTH = 1.525;   // X轴: 球桌宽度
        this.TABLE_HEIGHT = 0.05;   // Z轴: 桌面厚度
        this.NET_HEIGHT = 0.1525; // Z轴: 球网高度

        // --- 核心组件 ---
        this.scene = new THREE.Scene();

        this.camera = new THREE.PerspectiveCamera(75, this.container.clientWidth / this.container.clientHeight, 0.1, 1000);
        // 将相机放在桌子前方中央，并抬高，看向桌子中心
        this.camera.position.set(this.TABLE_WIDTH / 2, -1, 1.8);
        this.camera.lookAt(new THREE.Vector3(this.TABLE_WIDTH / 2, this.TABLE_LENGTH / 2, 0));

        this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.container.appendChild(this.renderer.domElement);

        // --- 光照 ---
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(-5, 10, 7.5); // 从左上方打光
        this.scene.add(directionalLight);

        // --- 控制器 ---
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.target.set(this.TABLE_WIDTH / 2, this.TABLE_LENGTH / 2, 0); // 让控制器围绕桌子中心旋转
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 1;
        this.controls.maxDistance = 10;
        this.controls.maxPolarAngle = Math.PI; // 允许相机看到桌底

        // --- 场景内容 ---
        this.createTable();
        this.createCustomAxes();
        this.createTrajectoryLine();

        // --- 实时轨迹元素 ---
        this.liveTrajectoryPoints = [];
        this.trajectoryPointsWithTime = []; // 存储带时间戳的轨迹点
        this.maxTrajectoryAge = 300; // 轨迹点最大存活时间（毫秒）

        const ballGeometry = new THREE.SphereGeometry(0.015, 16, 16); // 乒乓球半径约20mm，这里用15mm
        const ballMaterial = new THREE.MeshBasicMaterial({ color: 0xffa500 }); // Orange
        this.liveBallMesh = new THREE.Mesh(ballGeometry, ballMaterial);
        this.liveBallMesh.visible = false;
        this.scene.add(this.liveBallMesh);

        // --- 启动循环 ---
        window.addEventListener('resize', this.onWindowResize.bind(this));
        this.animate();

        // 定期清理过期轨迹点
        setInterval(() => {
            this.cleanupTrajectory();
        }, 50); // 每50ms清理一次，提高清理频率
    }

    createCustomAxes() {
        const AXIS_LENGTH = 0.4;

        // 原点：球桌的左前角 (用户坐标系原点)
        const origin = new THREE.Vector3(0, 0, 0);

        // X轴 (红色): 代表球桌宽度 (width)，从原点指向右。
        const dirX = new THREE.Vector3(1, 0, 0);
        const arrowX = new THREE.ArrowHelper(dirX, origin, AXIS_LENGTH, 0xff0000);
        this.scene.add(arrowX);

        // Y轴 (绿色): 代表球桌长度 (length)，从原点指向后。
        const dirY = new THREE.Vector3(0, 1, 0);
        const arrowY = new THREE.ArrowHelper(dirY, origin, AXIS_LENGTH, 0x00ff00);
        this.scene.add(arrowY);

        // Z轴 (蓝色): 代表高度 (height)，从原点指向上。
        const dirZ = new THREE.Vector3(0, 0, 1);
        const arrowZ = new THREE.ArrowHelper(dirZ, origin, AXIS_LENGTH, 0x0000ff);
        this.scene.add(arrowZ);

        // 添加一个醒目的球体来标记(0,0,0)原点
        const originSphereGeometry = new THREE.SphereGeometry(0.02, 16, 16); // 半径为2cm
        const originSphereMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 }); // 亮黄色
        const originSphere = new THREE.Mesh(originSphereGeometry, originSphereMaterial);
        originSphere.position.set(0, 0, 0); // 放置在场景原点
        this.scene.add(originSphere);
    }

    createTable() {
        // --- 桌面 ---
        // BoxGeometry的中心在(0,0,0), 我们需要移动它使其左前下角在(0,0,-厚度)
        const tableGeometry = new THREE.BoxGeometry(this.TABLE_WIDTH, this.TABLE_LENGTH, this.TABLE_HEIGHT);
        const tableMaterial = new THREE.MeshStandardMaterial({
            color: 0x0055aa,
            metalness: 0.3,
            roughness: 0.6
        });
        const table = new THREE.Mesh(tableGeometry, tableMaterial);
        // 移动Mesh，使其左前上角(桌面)与XY平面上的(0,0)对齐
        table.position.set(this.TABLE_WIDTH / 2, this.TABLE_LENGTH / 2, -this.TABLE_HEIGHT / 2);
        this.scene.add(table);

        // --- 中线 (沿长度方向) ---
        // 中线应该在 x = TABLE_WIDTH / 2
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.5 });
        const points = [];
        points.push(new THREE.Vector3(this.TABLE_WIDTH / 2, 0, 0.001));
        points.push(new THREE.Vector3(this.TABLE_WIDTH / 2, this.TABLE_LENGTH, 0.001));
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const centerLine = new THREE.Line(lineGeometry, lineMaterial);
        this.scene.add(centerLine);

        // --- 球网 ---
        // 球网在 y = TABLE_LENGTH / 2
        const netGeometry = new THREE.PlaneGeometry(this.TABLE_WIDTH, this.NET_HEIGHT);
        const netMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.2
        });
        const net = new THREE.Mesh(netGeometry, netMaterial);
        // 移动并放置球网
        net.position.set(this.TABLE_WIDTH / 2, this.TABLE_LENGTH / 2, this.NET_HEIGHT / 2);
        net.rotation.x = Math.PI / 2; // 绕X轴旋转90度使其竖直
        this.scene.add(net);
    }

    createTrajectoryLine() {
        // 创建支持渐变颜色的轨迹线材质
        const material = new THREE.LineBasicMaterial({
            vertexColors: true, // 启用顶点颜色
            linewidth: 3,
            transparent: true,
            opacity: 0.8
        });
        const geometry = new THREE.BufferGeometry();
        this.trajectoryLine = new THREE.Line(geometry, material);
        this.scene.add(this.trajectoryLine);
    }

    updateTrajectory(points) {
        // 当一个完整的、已计算好的轨迹被接收时，它会覆盖实时的轨迹
        this.liveTrajectoryPoints = [];
        this.liveBallMesh.visible = false;

        if (!points || points.length < 2) {
            this.trajectoryLine.geometry.setFromPoints([]);
            return;
        }
        
        const threePoints = points.map(p => new THREE.Vector3(p.pos_x, p.pos_y, p.pos_z));

        this.trajectoryLine.geometry.setFromPoints(threePoints);
        this.trajectoryLine.geometry.attributes.position.needsUpdate = true;
    }

    updateLiveTrajectory(balls) {
        const currentTime = Date.now();

        console.log('🎾 updateLiveTrajectory 被调用，球数据:', balls);

        // 如果有球被检测到，添加新的轨迹点
        if (balls && balls.length > 0) {
            const ball = balls[0]; // 暂时只追踪第一个检测到的球
            console.log('🎾 检测到球，位置:', ball.x, ball.y, ball.z);

            // 更新实时小球的位置
            this.liveBallMesh.position.set(ball.x, ball.y, ball.z);
            this.liveBallMesh.visible = true;

            // 添加新的轨迹点（带时间戳）
            this.trajectoryPointsWithTime.push({
                position: new THREE.Vector3(ball.x, ball.y, ball.z),
                timestamp: currentTime
            });

            console.log('🎾 轨迹点总数:', this.trajectoryPointsWithTime.length);
        } else {
            // 没有检测到球时，隐藏球体但保持轨迹
            this.liveBallMesh.visible = false;
            console.log('🎾 未检测到球');
        }

        // 清理过期的轨迹点
        this.trajectoryPointsWithTime = this.trajectoryPointsWithTime.filter(
            point => currentTime - point.timestamp < this.maxTrajectoryAge
        );

        // 更新轨迹线显示
        this.updateTrajectoryVisualization(currentTime);
    }

    updateTrajectoryVisualization(currentTime) {
        console.log('🌈 更新轨迹可视化，轨迹点数量:', this.trajectoryPointsWithTime.length);

        if (this.trajectoryPointsWithTime.length < 2) {
            // 如果轨迹点少于2个，清空轨迹线
            this.trajectoryLine.geometry.setFromPoints([]);
            console.log('🌈 轨迹点不足，清空轨迹线');
            return;
        }

        // 提取位置和计算颜色
        const positions = [];
        const colors = [];

        this.trajectoryPointsWithTime.forEach((point) => {
            positions.push(point.position);

            // 计算基于时间的颜色强度（越新越亮）
            const age = currentTime - point.timestamp;
            const intensity = Math.max(0.2, 1 - (age / this.maxTrajectoryAge));

            // 轨迹颜色：从亮蓝色渐变到暗蓝色
            const r = 0.0 * intensity;      // 红色分量
            const g = 0.67 * intensity;     // 绿色分量
            const b = 1.0 * intensity;      // 蓝色分量
            colors.push(r, g, b);
        });

        console.log('🌈 更新轨迹线，位置数量:', positions.length, '颜色数量:', colors.length / 3);

        // 更新几何体
        this.trajectoryLine.geometry.setFromPoints(positions);

        // 设置颜色属性（RGB，每个顶点3个分量）
        const colorAttribute = new THREE.Float32BufferAttribute(colors, 3);
        this.trajectoryLine.geometry.setAttribute('color', colorAttribute);

        this.trajectoryLine.geometry.attributes.position.needsUpdate = true;
        if (this.trajectoryLine.geometry.attributes.color) {
            this.trajectoryLine.geometry.attributes.color.needsUpdate = true;
        }
    }

    cleanupTrajectory() {
        const currentTime = Date.now();
        const oldLength = this.trajectoryPointsWithTime.length;

        // 清理过期的轨迹点
        this.trajectoryPointsWithTime = this.trajectoryPointsWithTime.filter(
            point => currentTime - point.timestamp < this.maxTrajectoryAge
        );

        // 如果有轨迹点被清理，更新可视化
        if (oldLength !== this.trajectoryPointsWithTime.length) {
            this.updateTrajectoryVisualization(currentTime);
        }
    }

    // 移除此方法，因为2D轨迹清理已在SimpleCameraDisplay类的drawTrajectory方法中处理

    animate() {
        requestAnimationFrame(this.animate.bind(this));
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }
}

/**
 * 交互式速度-时间图表组件
 * 支持缩放、平移、数据标签等高级交互功能
 */
class SpeedTimeInteractiveChart {
    constructor(canvasId, options = {}) {
        this.canvasId = canvasId;
        this.canvas = document.getElementById(canvasId);
        if (!this.canvas) {
            throw new Error(`Canvas element with id '${canvasId}' not found`);
        }

        // 配置选项
        this.options = {
            maxDataPoints: 1000,
            timeRange: 300, // 默认5分钟
            zoomSensitivity: 0.1,
            panSensitivity: 1.0,
            showCrosshair: true,
            showDataLabels: true,
            leftCameraColor: 'rgba(54, 162, 235, 1)', // 蓝色
            rightCameraColor: 'rgba(255, 99, 132, 1)', // 红色
            ...options
        };

        // 状态管理
        this.zoomLevel = 1.0;
        this.panOffset = { x: 0, y: 0 };
        this.isDragging = false;
        this.lastMousePos = { x: 0, y: 0 };
        this.crosshairPos = { x: 0, y: 0 };
        this.showLeftCamera = true;
        this.showRightCamera = false; // 默认隐藏右摄像头数据

        // 数据存储
        this.leftCameraData = [];
        this.rightCameraData = [];
        this.timeLabels = [];

        this.initChart();
        this.bindEvents();
    }

    initChart() {
        const ctx = this.canvas.getContext('2d');

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '左摄像头速度',
                        data: [],
                        borderColor: this.options.leftCameraColor,
                        backgroundColor: this.options.leftCameraColor.replace('1)', '0.1)'),
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointBackgroundColor: this.options.leftCameraColor,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        hidden: !this.showLeftCamera
                    },
                    {
                        label: '右摄像头速度',
                        data: [],
                        borderColor: this.options.rightCameraColor,
                        backgroundColor: this.options.rightCameraColor.replace('1)', '0.1)'),
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointBackgroundColor: this.options.rightCameraColor,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        hidden: !this.showRightCamera
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff',
                            usePointStyle: true
                        },
                        onClick: (e, legendItem) => {
                            this.toggleDataset(legendItem.datasetIndex);
                        }
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#ffffff',
                        borderWidth: 1,
                        callbacks: {
                            title: (context) => {
                                const timestamp = parseInt(context[0].label);
                                return this.formatTimestamp(timestamp);
                            },
                            label: (context) => {
                                const speed = context.parsed.y;
                                const cameraName = context.datasetIndex === 0 ? '左摄像头' : '右摄像头';
                                return `${cameraName}: ${speed.toFixed(2)} m/s`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: '时间',
                            color: '#a0a0a0'
                        },
                        ticks: {
                            color: '#a0a0a0',
                            callback: (value) => {
                                return this.formatTimeLabel(value);
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '速度 (m/s)',
                            color: '#a0a0a0'
                        },
                        ticks: {
                            color: '#a0a0a0'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                },
                onHover: (event, elements) => {
                    if (this.options.showCrosshair) {
                        this.updateCrosshair(event);
                    }
                }
            }
        });
    }

    bindEvents() {
        // 鼠标滚轮缩放
        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            this.handleZoom(e);
        });

        // 鼠标拖拽平移
        this.canvas.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            this.lastMousePos = { x: e.clientX, y: e.clientY };
            this.canvas.style.cursor = 'grabbing';
        });

        this.canvas.addEventListener('mousemove', (e) => {
            if (this.isDragging) {
                this.handlePan(e);
            }
            this.crosshairPos = { x: e.offsetX, y: e.offsetY };
        });

        this.canvas.addEventListener('mouseup', () => {
            this.isDragging = false;
            this.canvas.style.cursor = 'default';
        });

        this.canvas.addEventListener('mouseleave', () => {
            this.isDragging = false;
            this.canvas.style.cursor = 'default';
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.canvas.matches(':hover')) {
                this.handleKeyboard(e);
            }
        });
    }

    // 处理缩放
    handleZoom(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
        const oldZoomLevel = this.zoomLevel;
        this.zoomLevel *= zoomFactor;

        // 限制缩放范围
        this.zoomLevel = Math.max(0.1, Math.min(10, this.zoomLevel));

        console.log(`🖱️ 鼠标滚轮缩放: ${oldZoomLevel.toFixed(2)} -> ${this.zoomLevel.toFixed(2)}`);
        this.updateChartZoom();
    }

    // 处理平移
    handlePan(event) {
        const deltaX = event.clientX - this.lastMousePos.x;
        const deltaY = event.clientY - this.lastMousePos.y;

        // 修正平移方向：鼠标向右拖拽应该向右平移，向上拖拽应该向上平移
        this.panOffset.x -= deltaX * this.options.panSensitivity;
        this.panOffset.y += deltaY * this.options.panSensitivity;

        this.lastMousePos = { x: event.clientX, y: event.clientY };

        console.log(`🖱️ 拖拽平移: deltaX=${deltaX}, deltaY=${deltaY}, panOffset=(${this.panOffset.x.toFixed(1)}, ${this.panOffset.y.toFixed(1)})`);
        this.updateChartPan();
    }

    // 处理键盘事件
    handleKeyboard(event) {
        const step = 20; // 增加步长，让键盘平移更明显
        switch(event.key) {
            case 'ArrowLeft':
                // 左箭头：向左平移（显示更早的时间）
                this.panOffset.x += step;
                console.log(`⬅️ 左键平移: panOffset.x = ${this.panOffset.x}`);
                this.updateChartPan();
                break;
            case 'ArrowRight':
                // 右箭头：向右平移（显示更晚的时间）
                this.panOffset.x -= step;
                console.log(`➡️ 右键平移: panOffset.x = ${this.panOffset.x}`);
                this.updateChartPan();
                break;
            case 'ArrowUp':
                // 上箭头：向上平移（显示更高的速度）
                this.panOffset.y -= step;
                console.log(`⬆️ 上键平移: panOffset.y = ${this.panOffset.y}`);
                this.updateChartPan();
                break;
            case 'ArrowDown':
                // 下箭头：向下平移（显示更低的速度）
                this.panOffset.y += step;
                console.log(`⬇️ 下键平移: panOffset.y = ${this.panOffset.y}`);
                this.updateChartPan();
                break;
            case '+':
            case '=':
                this.zoomLevel *= 1.1;
                this.zoomLevel = Math.min(10, this.zoomLevel);
                this.updateChartZoom();
                break;
            case '-':
                this.zoomLevel *= 0.9;
                this.zoomLevel = Math.max(0.1, this.zoomLevel);
                this.updateChartZoom();
                break;
            case '0':
                this.resetView();
                break;
        }
    }

    // 更新图表缩放
    updateChartZoom() {
        if (!this.chart || !this.chart.options.scales) {
            console.log('❌ 图表或坐标轴不存在');
            return;
        }

        // 获取当前数据范围
        const allData = [...this.leftCameraData, ...this.rightCameraData];
        if (allData.length === 0) {
            console.log('❌ 没有数据可用于缩放');
            return;
        }

        console.log(`📊 数据点数量: 左摄像头=${this.leftCameraData.length}, 右摄像头=${this.rightCameraData.length}`);

        // 计算数据的时间和速度范围
        const timeValues = allData.map(point => point.x);
        const speedValues = allData.map(point => point.y);

        const minTime = Math.min(...timeValues);
        const maxTime = Math.max(...timeValues);
        const minSpeed = Math.min(...speedValues);
        const maxSpeed = Math.max(...speedValues);

        console.log(`📈 数据范围: 时间[${new Date(minTime).toLocaleTimeString()} - ${new Date(maxTime).toLocaleTimeString()}], 速度[${minSpeed.toFixed(2)} - ${maxSpeed.toFixed(2)}]`);

        // 计算缩放后的范围
        const timeRange = maxTime - minTime;
        const speedRange = maxSpeed - minSpeed;

        const zoomedTimeRange = timeRange / this.zoomLevel;
        const zoomedSpeedRange = speedRange / this.zoomLevel;

        // 计算中心点（考虑平移偏移）
        // 平移因子需要根据画布尺寸进行归一化
        const timePanFactor = this.panOffset.x / this.canvas.width;
        const speedPanFactor = this.panOffset.y / this.canvas.height;

        // 修正平移方向：
        // X轴：向右拖拽（正deltaX）应该显示更早的时间（负偏移）
        // Y轴：向上拖拽（正deltaY）应该显示更高的速度（正偏移）
        const centerTime = minTime + timeRange * 0.5 + timeRange * timePanFactor * 0.3;
        const centerSpeed = minSpeed + speedRange * 0.5 + speedRange * speedPanFactor * 0.3;

        // 设置新的坐标轴范围
        const newXMin = centerTime - zoomedTimeRange * 0.5;
        const newXMax = centerTime + zoomedTimeRange * 0.5;
        const newYMin = Math.max(0, centerSpeed - zoomedSpeedRange * 0.5);
        const newYMax = centerSpeed + zoomedSpeedRange * 0.5;

        this.chart.options.scales.x.min = newXMin;
        this.chart.options.scales.x.max = newXMax;
        this.chart.options.scales.y.min = newYMin;
        this.chart.options.scales.y.max = newYMax;

        console.log(`🔍 新坐标轴范围: X[${new Date(newXMin).toLocaleTimeString()} - ${new Date(newXMax).toLocaleTimeString()}], Y[${newYMin.toFixed(2)} - ${newYMax.toFixed(2)}]`);

        // 更新时间轴标签格式
        this.updateTimeAxisFormat();

        this.chart.update('none');
        console.log(`🔍 缩放级别: ${this.zoomLevel.toFixed(2)}x`);
    }

    // 更新图表平移
    updateChartPan() {
        if (!this.chart || !this.chart.options.scales) return;

        // 平移逻辑已经集成到 updateChartZoom 中
        this.updateChartZoom();
    }

    // 更新时间轴格式
    updateTimeAxisFormat() {
        if (!this.chart || !this.chart.options.scales.x.ticks) return;

        // 根据缩放级别调整时间标签精度
        this.chart.options.scales.x.ticks.callback = (value) => {
            return this.formatTimeLabel(value);
        };
    }

    // 重置视图
    resetView() {
        this.zoomLevel = 1.0;
        this.panOffset = { x: 0, y: 0 };

        // 重置坐标轴到自动范围
        if (this.chart && this.chart.options.scales) {
            delete this.chart.options.scales.x.min;
            delete this.chart.options.scales.x.max;
            delete this.chart.options.scales.y.min;
            this.chart.options.scales.y.max = undefined;
            this.chart.options.scales.y.beginAtZero = true;

            this.chart.update('none');
        }

        console.log('🏠 视图已重置');
    }

    // 切换数据集显示
    toggleDataset(datasetIndex) {
        if (datasetIndex === 0) {
            this.showLeftCamera = !this.showLeftCamera;
            this.chart.data.datasets[0].hidden = !this.showLeftCamera;
        } else if (datasetIndex === 1) {
            this.showRightCamera = !this.showRightCamera;
            this.chart.data.datasets[1].hidden = !this.showRightCamera;
        }
        this.chart.update();
    }

    // 格式化时间戳
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化时间标签
    formatTimeLabel(value) {
        // 根据缩放级别调整时间标签精度
        if (this.zoomLevel > 5) {
            // 高缩放：显示秒
            return new Date(value).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } else if (this.zoomLevel > 1) {
            // 中等缩放：显示分钟
            return new Date(value).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            // 低缩放：显示小时
            return new Date(value).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    // 更新十字准线
    updateCrosshair(event) {
        // 十字准线功能将在后续实现
        // 需要自定义Canvas绘制
    }

    // 更新数据
    async updateData(timeRange = this.options.timeRange) {
        try {
            const currentTime = Date.now();
            const startTime = currentTime - (timeRange * 1000);

            // 只查询左摄像头数据（根据用户需求）
            const leftQuery = `
                SELECT timestamp_ms, speed FROM trajectory
                WHERE timestamp_ms > ${startTime} AND camera_id = 1
                ORDER BY timestamp_ms ASC
            `;

            const leftResponse = await fetch('/api/db/query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query: leftQuery })
            });

            if (!leftResponse.ok) {
                throw new Error('数据查询失败');
            }

            const leftData = await leftResponse.json();

            // 只处理左摄像头数据，右摄像头数据设为空数组
            this.processData(leftData.rows, []);
            this.chart.update();

        } catch (error) {
            console.error('❌ 更新交互式速度图表数据失败:', error);
        }
    }

    // 处理数据
    processData(leftRows, rightRows) {
        console.log(`📊 处理数据: 左摄像头${leftRows.length}行, 右摄像头${rightRows.length}行`);

        // 只处理左摄像头数据的时间戳
        const leftTimestamps = leftRows.map(row => parseInt(row[0])).sort((a, b) => a - b);
        console.log(`📊 左摄像头时间戳数量: ${leftTimestamps.length}`);

        if (leftTimestamps.length > 0) {
            const firstTime = new Date(leftTimestamps[0]).toLocaleTimeString();
            const lastTime = new Date(leftTimestamps[leftTimestamps.length - 1]).toLocaleTimeString();
            console.log(`📊 时间范围: ${firstTime} - ${lastTime}`);
        }

        // 创建左摄像头数据映射，并应用静止球判断
        const leftDataMap = new Map();
        leftRows.forEach(row => {
            const timestamp = parseInt(row[0]);
            let speed = parseFloat(row[1]);

            // 如果球速小于0.01，认为是静止的，设置为0
            if (speed < 0.01) {
                speed = 0;
            }

            leftDataMap.set(timestamp, speed);
        });

        // 构建左摄像头图表数据
        const leftChartData = [];
        leftTimestamps.forEach(timestamp => {
            if (leftDataMap.has(timestamp)) {
                leftChartData.push({ x: timestamp, y: leftDataMap.get(timestamp) });
            }
        });

        // 数据采样（如果数据点太多）
        const maxPoints = this.options.maxDataPoints;
        if (leftChartData.length > maxPoints) {
            this.leftCameraData = this.sampleData(leftChartData, maxPoints);
        } else {
            this.leftCameraData = leftChartData;
        }

        // 右摄像头数据设为空（根据用户需求）
        this.rightCameraData = [];

        // 更新图表数据
        this.chart.data.datasets[0].data = this.leftCameraData;
        this.chart.data.datasets[1].data = this.rightCameraData;

        console.log(`📊 最终数据: 左摄像头${this.leftCameraData.length}点, 右摄像头${this.rightCameraData.length}点`);

        // 如果有数据，输出一些样本
        if (this.leftCameraData.length > 0) {
            const sample = this.leftCameraData[0];
            console.log(`📊 左摄像头样本: 时间=${new Date(sample.x).toLocaleTimeString()}, 速度=${sample.y.toFixed(2)}m/s`);

            // 统计静止点数量
            const staticCount = this.leftCameraData.filter(point => point.y === 0).length;
            console.log(`📊 静止点数量: ${staticCount}/${this.leftCameraData.length}`);
        }
    }

    // 数据采样
    sampleData(data, maxPoints) {
        if (data.length <= maxPoints) return data;

        const step = Math.ceil(data.length / maxPoints);
        const sampledData = [];

        for (let i = 0; i < data.length; i += step) {
            sampledData.push(data[i]);
        }

        return sampledData;
    }

    // 销毁图表
    destroy() {
        if (this.chart) {
            this.chart.destroy();
        }

        // 移除事件监听器
        this.canvas.removeEventListener('wheel', this.handleZoom);
        this.canvas.removeEventListener('mousedown', this.handleMouseDown);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('mouseup', this.handleMouseUp);
        this.canvas.removeEventListener('mouseleave', this.handleMouseLeave);
    }
}

// 创建应用实例并暴露给全局作用域（用于测试）
const app = new SimpleCameraDisplay();
window.app = app; // 暴露给测试脚本使用

