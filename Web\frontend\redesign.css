/* Camera_Editor 重新设计样式 */

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: none;
    margin: 0;
    padding: 0;
    gap: 0;
}

/* 顶部导航栏 */
.top-navbar {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.95) 100%);
    border-bottom: 2px solid var(--border-color);
    padding: 12px 24px;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1800px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-section i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version {
    background: var(--accent-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* 系统状态栏 */
.system-status-bar {
    display: flex;
    align-items: center;
    gap: 24px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    color: var(--text-muted-color);
}

.status-item i {
    color: var(--primary-color);
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-actions .btn {
    padding: 8px 12px;
    min-width: auto;
    border-radius: 6px;
}

/* 标签页导航 */
.tab-navigation {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    overflow-x: auto;
}

.tab-container {
    display: flex;
    max-width: 1800px;
    margin: 0 auto;
}

.tab-button {
    background: none;
    border: none;
    color: var(--text-muted-color);
    padding: 16px 24px;
    cursor: pointer;
    transition: all var(--transition-speed);
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    white-space: nowrap;
}

.tab-button:hover {
    color: var(--text-color);
    background: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}

.tab-button i {
    font-size: 1.1rem;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 24px;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
}

/* 标签页内容 */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 录制中心布局 */
.recording-layout {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.recording-modes .mode-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.mode-card {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 46, 0.9) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: all var(--transition-speed);
}

.mode-card:hover {
    border-color: var(--border-glow);
    box-shadow: var(--subtle-glow);
}

.mode-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.mode-header i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.mode-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.mode-content {
    margin-bottom: 16px;
}

.mode-content p {
    color: var(--text-muted-color);
    margin-bottom: 16px;
    font-size: 0.9rem;
}

.mode-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-muted-color);
}

/* 个人摄像头控制 */
.individual-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.camera-control {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.camera-control span {
    min-width: 80px;
    font-weight: 500;
}

/* 设置布局 */
.settings-layout {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 16px;
}

.setting-group {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    margin-bottom: 12px;
}

.setting-description {
    font-size: 0.85rem;
    color: var(--text-muted-color);
    margin-top: 8px;
    margin-bottom: 0;
}

/* 配置网格 */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.config-group {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.config-group h3 {
    margin: 0 0 16px 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.config-item:last-child {
    margin-bottom: 0;
}

.config-select,
.config-input {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 4px;
    min-width: 120px;
}

/* 底部状态栏 */
.bottom-status-bar {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 8px 24px;
    font-size: 0.8rem;
}

.status-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1800px;
    margin: 0 auto;
}

.status-info,
.performance-info {
    display: flex;
    gap: 20px;
}

.status-info .status-item,
.performance-info .status-item {
    color: var(--text-muted-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-content {
        flex-direction: column;
        gap: 12px;
    }
    
    .system-status-bar {
        gap: 12px;
    }
    
    .tab-container {
        overflow-x: auto;
    }
    
    .main-content {
        padding: 16px;
    }
    
    .recording-modes .mode-grid {
        grid-template-columns: 1fr;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
}
