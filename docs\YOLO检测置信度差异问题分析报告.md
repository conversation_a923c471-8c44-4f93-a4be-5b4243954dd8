# YOLO检测置信度差异问题分析报告

## 问题描述

**现象**: 左摄像头YOLO检测置信度始终比右摄像头低很多
**影响**: 可能导致3D重建匹配失败，影响系统整体检测精度

## 技术分析

### 1. 代码架构分析

#### YOLO推理流程
```cpp
// Main/app_lifecycle.cpp - 双摄像头独立推理
if (main_loop_frame_counter % AI_FRAME_INTERVAL == 0) {
    float current_threshold = shared_data->getConfidenceThreshold();
    auto inference_result = inference_service->processFrameWithRaw(frame, current_threshold);
    
    // 存储检测结果
    shared_data->setDetectionResult(camera_id, inference_result.detection_result);
    shared_data->setRawYoloDetections(camera_id, inference_result.raw_yolo_detections);
}
```

#### 关键发现
1. **统一推理配置**: 左右摄像头使用相同的YOLO模型和置信度阈值
2. **独立推理实例**: 每个摄像头线程有独立的InferenceService实例
3. **相同处理流程**: 两个摄像头使用完全相同的推理代码路径

### 2. 可能原因分析

#### A. 相机硬件差异 ⭐⭐⭐⭐⭐
**最可能的原因**

<augment_code_snippet path="Services/CameraService.hpp" mode="EXCERPT">
````cpp
// 相机ID常量
static constexpr const char* idLeftCamera = "*********";
static constexpr const char* idRightCamera = "*********";
````
</augment_code_snippet>

**分析**:
- 不同的相机硬件可能有不同的图像传感器特性
- 镜头质量、焦距、光圈设置可能不同
- 图像预处理参数可能存在差异

#### B. 相机标定质量差异 ⭐⭐⭐⭐
**重要影响因素**

<augment_code_snippet path="Camera/dualEye.hpp" mode="EXCERPT">
````cpp
cv::Mat cameraMatrixLeft = (cv::Mat_<double>(3, 3) <<
    1.212461773900977e+03,0,0,
    0,1.208948173072893e+03,0,
    7.125999578992373e+02,5.582923658050460e+02,1);

cv::Mat cameraMatrixRight = (cv::Mat_<double>(3, 3) <<
    1.211146774417913e+03,0,0,
    0,1.208369255991100e+03,0,
    7.131739667255839e+02,5.533248398722095e+02,1);
````
</augment_code_snippet>

**分析**:
- 左右相机的内参矩阵略有不同
- 畸变系数差异可能影响图像质量
- 标定精度差异会影响图像矫正效果

#### C. 光照条件差异 ⭐⭐⭐
**环境因素**

- 左右摄像头可能面对不同的光照条件
- 阴影、反光等环境因素影响图像质量
- 自动曝光、白平衡设置可能不同

#### D. 图像预处理差异 ⭐⭐
**软件处理**

<augment_code_snippet path="Deploy/yolo.cpp" mode="EXCERPT">
````cpp
deploy::InferOption option;
option.enableSwapRB();
// option.setNormalizeParams({0.485, 0.456, 0.406}, {0.229, 0.224, 0.225});
model = std::make_unique<deploy::DetectModel>(engine_path, option);
````
</augment_code_snippet>

**分析**:
- 图像预处理参数对所有摄像头相同
- 但输入图像质量差异会影响最终结果

### 3. 诊断方案

#### 步骤1: 图像质量对比分析
```cpp
// 在InferenceService中添加图像质量分析
void analyzeImageQuality(const cv::Mat& frame, int camera_id) {
    // 计算图像统计信息
    cv::Scalar mean, stddev;
    cv::meanStdDev(frame, mean, stddev);
    
    // 计算图像清晰度（拉普拉斯方差）
    cv::Mat gray, laplacian;
    cv::cvtColor(frame, gray, cv::COLOR_BGR2GRAY);
    cv::Laplacian(gray, laplacian, CV_64F);
    cv::Scalar laplacian_mean, laplacian_stddev;
    cv::meanStdDev(laplacian, laplacian_mean, laplacian_stddev);
    
    UTF8Utils::println("相机" + std::to_string(camera_id) + 
                      " 亮度均值: " + std::to_string(mean[0]) +
                      " 清晰度: " + std::to_string(laplacian_stddev[0]));
}
```

#### 步骤2: 检测结果详细对比
```cpp
// 在StereoReconstructionService中添加置信度分析
void analyzeConfidenceDifference(
    const std::map<std::string, std::vector<Yolo::Detection>>& leftDetections,
    const std::map<std::string, std::vector<Yolo::Detection>>& rightDetections) {
    
    for (const auto& [className, leftBalls] : leftDetections) {
        if (rightDetections.find(className) != rightDetections.end()) {
            const auto& rightBalls = rightDetections.at(className);
            
            for (const auto& leftBall : leftBalls) {
                for (const auto& rightBall : rightBalls) {
                    float confDiff = rightBall.conf - leftBall.conf;
                    if (std::abs(confDiff) > 0.1f) {
                        UTF8Utils::println("置信度差异: 左=" + std::to_string(leftBall.conf) +
                                         " 右=" + std::to_string(rightBall.conf) +
                                         " 差值=" + std::to_string(confDiff));
                    }
                }
            }
        }
    }
}
```

#### 步骤3: 相机参数验证
```cpp
// 验证相机设置是否一致
void verifyCameraSettings() {
    // 检查曝光时间、增益、白平衡等参数
    // 确保两个相机使用相同的图像采集参数
}
```

## 解决方案

### 方案1: 相机硬件校准 ⭐⭐⭐⭐⭐
**推荐优先级: 最高**

1. **检查相机物理设置**
   - 验证两个相机的镜头是否相同型号
   - 检查光圈、焦距设置是否一致
   - 确认安装位置和角度是否对称

2. **统一相机参数**
   - 设置相同的曝光时间
   - 统一增益设置
   - 同步白平衡参数

### 方案2: 软件补偿机制 ⭐⭐⭐⭐
**实现置信度校正**

```cpp
// 在InferenceService中添加置信度校正
class ConfidenceCalibrator {
private:
    float m_leftCameraBoost = 1.0f;  // 左摄像头置信度提升系数
    float m_rightCameraBoost = 1.0f; // 右摄像头置信度提升系数
    
public:
    void calibrateConfidence(std::map<std::string, std::vector<Yolo::Detection>>& detections, 
                           int camera_id) {
        float boost = (camera_id == 1) ? m_leftCameraBoost : m_rightCameraBoost;
        
        for (auto& [className, detectionList] : detections) {
            for (auto& detection : detectionList) {
                detection.conf = std::min(1.0f, detection.conf * boost);
            }
        }
    }
    
    void setCalibrationFactors(float leftBoost, float rightBoost) {
        m_leftCameraBoost = leftBoost;
        m_rightCameraBoost = rightBoost;
    }
};
```

### 方案3: 动态阈值调整 ⭐⭐⭐
**基于相机ID的自适应阈值**

```cpp
// 在SharedData中添加相机特定的置信度阈值
class SharedData {
private:
    float m_confidenceThresholdLeft = 0.4f;
    float m_confidenceThresholdRight = 0.4f;
    
public:
    float getConfidenceThreshold(int camera_id) const {
        return (camera_id == 1) ? m_confidenceThresholdLeft : m_confidenceThresholdRight;
    }
    
    void setConfidenceThreshold(int camera_id, float threshold) {
        if (camera_id == 1) {
            m_confidenceThresholdLeft = threshold;
        } else {
            m_confidenceThresholdRight = threshold;
        }
    }
};
```

### 方案4: 重新标定系统 ⭐⭐
**提升标定精度**

1. **重新进行双目标定**
   - 使用更高精度的标定板
   - 增加标定图像数量
   - 优化标定算法参数

2. **验证标定结果**
   - 检查重投影误差
   - 验证立体匹配精度

## 实施建议

### 立即行动 (1-2天)
1. **添加诊断代码**: 实施图像质量分析和置信度对比
2. **检查硬件设置**: 验证相机物理参数是否一致
3. **收集数据**: 记录一段时间的置信度差异数据

### 短期解决 (3-5天)
1. **实施软件补偿**: 添加置信度校正机制
2. **优化相机参数**: 统一两个相机的采集设置
3. **测试验证**: 验证解决方案效果

### 长期优化 (1-2周)
1. **硬件升级**: 如需要，考虑更换相机或镜头
2. **系统重标定**: 提升整体标定精度
3. **算法优化**: 基于数据分析结果进一步优化

## 预期效果

- **置信度差异**: 从当前的显著差异降低到±0.05以内
- **3D重建成功率**: 提升10-20%
- **系统稳定性**: 减少因置信度差异导致的匹配失败

## 风险评估

- **低风险**: 软件补偿方案，可快速实施和回滚
- **中风险**: 相机参数调整，需要仔细测试
- **高风险**: 硬件更换，需要重新标定整个系统
