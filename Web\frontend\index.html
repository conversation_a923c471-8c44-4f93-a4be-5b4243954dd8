<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双目识别系统</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-video"></i>
                    <h1>双目识别系统</h1>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="connection-status">连接中...</span>
                </div>
                <div class="fps-indicator">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-display">0 FPS</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 关键指标仪表板 -->
            <section class="dashboard-metrics">
                <h2><i class="fas fa-chart-line"></i> 实时监控仪表板</h2>
                <div class="metrics-grid">
                    <div class="metric-card" id="metric-ball-speed" title="基于SG滤波器计算的瞬时速度">
                        <div class="metric-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="ballSpeed">0.00 <span class="unit">m/s</span></div>
                            <div class="metric-label">实时球速</div>
                        </div>
                    </div>
                    <div class="metric-card" id="metric-ball-count">
                        <div class="metric-icon">
                            <i class="fas fa-table-tennis-paddle-ball"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="ballCount">0</div>
                            <div class="metric-label">检测目标</div>
                        </div>
                    </div>
                    <div class="metric-card" id="metric-fps">
                        <div class="metric-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="dashboard-fps-display">0 FPS</div>
                            <div class="metric-label">接收帧率</div>
                        </div>
                    </div>
                    <div class="metric-card" id="metric-last-update">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value" id="coordLastUpdate">--</div>
                            <div class="metric-label">最后更新</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 控制面板 -->
            <section class="control-dashboard">
                <div class="global-controls">
                    <div class="control-panel">
                        <h2><i class="fas fa-gamepad"></i> 系统控制</h2>
                        <div class="controls">
                            <button class="btn btn-primary" id="startBtn"><i class="fas fa-play"></i> 启动系统</button>
                            <button class="btn" id="stopBtn"><i class="fas fa-stop"></i> 停止系统</button>
                            <button class="btn btn-record-all" id="recordAllBtn"><i class="fas fa-video"></i> 全部录制</button>
                            <button class="btn btn-calibration" id="calibrationBtn"><i class="fas fa-crosshairs"></i> 相机标定</button>
                            <button class="btn" id="refreshBtn"><i class="fas fa-sync-alt"></i> 刷新连接</button>
                        </div>

                        <!-- 新增：YOLO检测框录制控制 -->
                        <div class="detection-recording-controls">
                            <h3><i class="fas fa-square-check"></i> 检测框录制控制</h3>
                            <div class="detection-controls">
                                <div class="control-group">
                                    <label class="switch">
                                        <input type="checkbox" id="detectionRecordingToggle">
                                        <span class="slider round"></span>
                                    </label>
                                    <span class="control-label">启用检测框录制</span>
                                </div>
                                <div class="control-group">
                                    <label for="recordingModeSelect">录制模式:</label>
                                    <select id="recordingModeSelect" class="mode-select">
                                        <option value="RAW_MODE">原始视频</option>
                                        <option value="DETECTION_MODE">带检测框</option>
                                    </select>
                                </div>
                                <div class="status-indicator">
                                    <div class="status-dot" id="detectionRecordingStatus"></div>
                                    <span id="detectionRecordingStatusText">未启用</span>
                                </div>
                            </div>
                        </div>

                        <!-- 精彩录制控制 -->
                        <div class="highlight-controls">
                            <h3><i class="fas fa-star"></i> 精彩片段录制</h3>
                            <div class="controls">
                                <button class="btn btn-highlight-start" id="highlightStartBtn">
                                    <i class="fas fa-record-vinyl"></i> 开始精彩录制
                                </button>
                                <button class="btn btn-highlight-stop" id="highlightStopBtn" style="display: none;">
                                    <i class="fas fa-cut"></i> 停止并生成精彩片段
                                </button>
                            </div>
                            <div class="highlight-status">
                                <div class="status-item">
                                    <span class="label">录制状态:</span>
                                    <span class="value" id="highlightStatus">未开始</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">录制时长:</span>
                                    <span class="value" id="highlightDuration">0s</span>
                                </div>
                                <div class="status-item">
                                    <span class="label">精彩时刻:</span>
                                    <span class="value" id="highlightMoments">0</span>
                                </div>
                            </div>
                            <div class="highlight-settings">
                                <div class="setting-item">
                                    <label for="speedThreshold"><i class="fas fa-tachometer-alt"></i> 球速阈值 (m/s)</label>
                                    <div class="slider-container">
                                        <input type="range" id="speedThreshold" min="5" max="30" value="15" step="0.5">
                                        <span id="speedThresholdValue">15.0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="threshold-control">
                            <label for="confidenceThreshold"><i class="fas fa-bullseye"></i> AI检测阈值</label>
                            <div class="slider-container">
                                <input type="range" id="confidenceThreshold" name="confidenceThreshold" min="0.1" max="0.9" value="0.4" step="0.05">
                                <span id="thresholdValue">0.40</span>
                            </div>
                        </div>
                    </div>
                    <div class="status-panel">
                        <h2><i class="fas fa-heartbeat"></i> 系统状态</h2>
                        <div class="status-grid">
                            <div class="status-card" id="camera-status-card">
                                <div class="status-icon"><i class="fas fa-camera"></i></div>
                                <div class="status-info">
                                    <div class="status-title">相机系统</div>
                                    <div class="status-value" id="cameraSystemStatus">未启动</div>
                                </div>
                            </div>
                            <div class="status-card" id="stream-status-card">
                                <div class="status-icon"><i class="fas fa-satellite-dish"></i></div>
                                <div class="status-info">
                                    <div class="status-title">视频流</div>
                                    <div class="status-value" id="streamStatus">已停止</div>
                                </div>
                            </div>
                            <div class="status-card" id="recording-status-card">
                                <div class="status-icon"><i class="fas fa-record-vinyl"></i></div>
                                <div class="status-info">
                                    <div class="status-title">全局录制</div>
                                    <div class="status-value" id="recordingStatus">未录制</div>
                                </div>
                            </div>
                            <div class="status-card" id="calibration-status-card">
                                <div class="status-icon"><i class="fas fa-crosshairs"></i></div>
                                <div class="status-info">
                                    <div class="status-title">相机标定</div>
                                    <div class="status-value" id="calibrationStatus">空闲</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 摄像头画面 -->
            <section class="camera-display">
                <div class="camera-grid">
                    <div class="camera-item" id="camera-item-1">
                        <div class="camera-header">
                            <h3><i class="fas fa-camera"></i> 左摄像头 (ID: 1)</h3>
                            <div class="camera-status status-disconnected" id="leftCameraStatus">
                                <span class="status-text">等待连接</span>
                            </div>
                        </div>
                        <div class="camera-view">
                            <canvas id="leftCamera" class="video-canvas"></canvas>
                            <canvas id="leftTrajectoryCanvas" class="trajectory-canvas"></canvas>
                            <div class="no-signal" id="leftNoSignal"><i class="fas fa-power-off"></i><p>无信号</p></div>
                        </div>
                        <div class="camera-footer">
                             <div class="camera-stats">
                                <div class="stat-item" title="后端图像处理和AI推理的实际帧率">
                                    <i class="fas fa-microchip"></i>
                                    <span class="stat-value" id="fps-cam1">-- FPS</span>
                                </div>
                            </div>
                            <div class="camera-controls">
                                <button class="btn btn-record" id="startRec1"><i class="fas fa-circle"></i> 录制</button>
                                <button class="btn btn-stop-rec" id="stopRec1" style="display:none;"><i class="fas fa-square"></i> 停止</button>
                                <div class="rec-status" id="recStatus1">
                                    <div class="rec-dot"></div>
                                    <span>未录制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="camera-item" id="camera-item-2">
                        <div class="camera-header">
                            <h3><i class="fas fa-camera"></i> 右摄像头 (ID: 2)</h3>
                            <div class="camera-status status-disconnected" id="rightCameraStatus">
                                <span class="status-text">等待连接</span>
                            </div>
                        </div>
                        <div class="camera-view">
                            <canvas id="rightCamera" class="video-canvas"></canvas>
                            <canvas id="rightTrajectoryCanvas" class="trajectory-canvas"></canvas>
                             <div class="no-signal" id="rightNoSignal"><i class="fas fa-power-off"></i><p>无信号</p></div>
                        </div>
                         <div class="camera-footer">
                            <div class="camera-stats">
                                 <div class="stat-item" title="后端图像处理和AI推理的实际帧率">
                                    <i class="fas fa-microchip"></i>
                                    <span class="stat-value" id="fps-cam2">-- FPS</span>
                                </div>
                            </div>
                            <div class="camera-controls">
                                <button class="btn btn-record" id="startRec2"><i class="fas fa-circle"></i> 录制</button>
                                <button class="btn btn-stop-rec" id="stopRec2" style="display:none;"><i class="fas fa-square"></i> 停止</button>
                                <div class="rec-status" id="recStatus2">
                                    <div class="rec-dot"></div>
                                    <span>未录制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 实时关键数据 -->
            
            <!-- 相机标定面板 -->
            <section class="calibration-panel" id="calibrationPanel" style="display: none;">
                <div class="panel-header">
                    <h2><i class="fas fa-crosshairs"></i> 相机标定控制</h2>
                    <div class="calibration-status-indicator">
                        <div class="status-dot" id="calibrationStatusDot"></div>
                        <span id="calibrationStatusText">空闲</span>
                    </div>
                </div>
                <div class="calibration-content">
                    <div class="calibration-instructions">
                        <h3><i class="fas fa-info-circle"></i> 标定说明</h3>
                        <ol>
                            <li>确保双目摄像头已正常启动并显示画面</li>
                            <li>将8×11棋盘格标定板放置在摄像头视野内</li>
                            <li>确保标定板完整、清晰、无遮挡</li>
                            <li>点击"开始标定"按钮，系统将自动检测并标定</li>
                        </ol>
                    </div>
                    <div class="calibration-controls">
                        <button class="btn btn-calibration-start" id="startCalibrationBtn">
                            <i class="fas fa-play"></i> 开始标定
                        </button>
                        <button class="btn btn-calibration-stop" id="stopCalibrationBtn" style="display: none;">
                            <i class="fas fa-stop"></i> 停止标定
                        </button>
                    </div>
                    <div class="calibration-progress" id="calibrationProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="calibrationProgressFill"></div>
                        </div>
                        <div class="progress-text" id="calibrationProgressText">检测标定板中...</div>
                    </div>
                    <div class="calibration-results" id="calibrationResults" style="display: none;">
                        <h3><i class="fas fa-check-circle"></i> 标定结果</h3>
                        <div class="result-item">
                            <span class="result-label">状态:</span>
                            <span class="result-value" id="calibrationResultStatus">--</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">平均误差:</span>
                            <span class="result-value" id="calibrationResultError">-- mm</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">检测角点:</span>
                            <span class="result-value" id="calibrationResultCorners">--</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">完成时间:</span>
                            <span class="result-value" id="calibrationResultTime">--</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 数据分析与可视化 -->
            <div class="analysis-section">
                <!-- 数据可视化仪表板 -->
                <section class="data-visualization-dashboard">
                    <div class="panel-header">
                        <h2><i class="fas fa-chart-bar"></i> 数据可视化分析</h2>
                        <div class="visualization-controls">
                            <button class="btn btn-small" id="refreshChartsBtn"><i class="fas fa-sync-alt"></i> 刷新图表</button>
                            <button class="btn btn-small" id="exportDataBtn"><i class="fas fa-download"></i> 导出数据</button>
                        <button class="btn btn-small" id="filterToggleBtn"><i class="fas fa-filter"></i> 筛选器</button>
                        </div>
                    </div>
                    <div class="visualization-grid">
                        <!-- 球速分布图 -->
                        <div class="chart-container" style="display: none;">
                            <div class="chart-header">
                                <h3><i class="fas fa-tachometer-alt"></i> 球速分布</h3>
                                <div class="chart-status" id="speedChartStatus">加载中...</div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="speedDistributionChart"></canvas>
                            </div>
                        </div>

                        <!-- 时间序列图 -->
                        <div class="chart-container" style="display: none;">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-line"></i> 速度时间序列</h3>
                                <div class="chart-controls">
                                    <select id="timeRangeSelect" class="chart-select">
                                        <option value="60">最近1分钟</option>
                                        <option value="300">最近5分钟</option>
                                        <option value="1800">最近30分钟</option>
                                        <option value="3600">最近1小时</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="speedTimeSeriesChart"></canvas>
                            </div>
                        </div>

                        <!-- 交互式速度分析 - 主要重点组件 -->
                        <div class="chart-container featured-chart">
                            <div class="chart-header">
                                <h3><i class="fas fa-expand-arrows-alt"></i> 交互式速度分析 <span class="featured-badge">重点分析</span></h3>
                                <div class="chart-controls">
                                    <div class="interactive-chart-controls">
                                        <button class="btn btn-small" id="zoomInBtn" title="放大">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button class="btn btn-small" id="zoomOutBtn" title="缩小">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button class="btn btn-small" id="resetViewBtn" title="重置视图">
                                            <i class="fas fa-home"></i>
                                        </button>
                                        <button class="btn btn-small" id="refreshInteractiveBtn" title="手动刷新数据">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                        <div class="camera-toggles">
                                            <label class="camera-toggle">
                                                <input type="checkbox" id="leftCameraToggle" checked>
                                                <span class="toggle-label left-camera">左摄像头</span>
                                            </label>
                                            <label class="camera-toggle" style="display: none;">
                                                <input type="checkbox" id="rightCameraToggle">
                                                <span class="toggle-label right-camera">右摄像头</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="chart-status" id="interactiveChartStatus">准备中...</div>
                                </div>
                            </div>
                            <div class="chart-wrapper interactive-chart-wrapper featured-wrapper">
                                <canvas id="speedTimeInteractiveChart"></canvas>
                                <div class="chart-instructions">
                                    <div class="instruction-item">
                                        <i class="fas fa-mouse"></i>
                                        <span>滚轮缩放 | 拖拽平移</span>
                                    </div>
                                    <div class="instruction-item">
                                        <i class="fas fa-keyboard"></i>
                                        <span>方向键平移 | +/- 缩放 | 0 重置</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 运动轨迹热力图 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-fire"></i> 运动热力图</h3>
                                <div class="chart-status" id="heatmapStatus">准备中...</div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="trajectoryHeatmapChart"></canvas>
                            </div>
                        </div>

                        <!-- 运动频率分析 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-wave-square"></i> 运动频率分析</h3>
                                <div class="chart-status" id="frequencyStatus">分析中...</div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="movementFrequencyChart"></canvas>
                            </div>
                        </div>

                        <!-- 球速心电图 -->
                        <div class="chart-container" style="display: none;">
                            <div class="chart-header">
                                <h3><i class="fas fa-heartbeat"></i> 球速心电图</h3>
                                <div class="chart-status" id="ecgStatus">监测中...</div>
                                <div class="ecg-current-speed">
                                    <span class="ecg-speed-label">当前球速:</span>
                                    <span class="ecg-speed-value" id="ecgCurrentSpeed">0.0</span>
                                    <span class="ecg-speed-unit">m/s</span>
                                </div>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="speedEcgChart"></canvas>
                            </div>
                        </div>

                        <!-- 统计摘要 -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3><i class="fas fa-calculator"></i> 统计摘要</h3>
                                <div class="chart-status" id="statsStatus">计算中...</div>
                            </div>
                            <div class="stats-wrapper">
                                <div class="stat-item">
                                    <div class="stat-label">平均速度</div>
                                    <div class="stat-value" id="avgSpeed">-- m/s</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">最大速度</div>
                                    <div class="stat-value" id="maxSpeed">-- m/s</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">数据点数</div>
                                    <div class="stat-value" id="dataPointCount">--</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">活跃时长</div>
                                    <div class="stat-value" id="activeDuration">-- 分钟</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 数据筛选器面板 -->
                <section class="data-filter-panel" id="dataFilterPanel" style="display: none;">
                    <div class="panel-header">
                        <h2><i class="fas fa-filter"></i> 数据筛选器</h2>
                        <button class="btn btn-small" id="applyFiltersBtn"><i class="fas fa-check"></i> 应用筛选</button>
                    </div>
                    <div class="filter-grid">
                        <!-- 时间范围筛选 -->
                        <div class="filter-group">
                            <label class="filter-label">时间范围</label>
                            <div class="filter-controls">
                                <input type="datetime-local" id="startTimeFilter" class="filter-input">
                                <span class="filter-separator">至</span>
                                <input type="datetime-local" id="endTimeFilter" class="filter-input">
                            </div>
                        </div>

                        <!-- 速度范围筛选 -->
                        <div class="filter-group">
                            <label class="filter-label">速度范围 (m/s)</label>
                            <div class="filter-controls">
                                <input type="number" id="minSpeedFilter" class="filter-input" placeholder="最小速度" min="0" step="0.1">
                                <span class="filter-separator">至</span>
                                <input type="number" id="maxSpeedFilter" class="filter-input" placeholder="最大速度" min="0" step="0.1">
                            </div>
                        </div>

                        <!-- 摄像头筛选 -->
                        <div class="filter-group">
                            <label class="filter-label">摄像头</label>
                            <div class="filter-controls">
                                <select id="cameraFilter" class="filter-select">
                                    <option value="">全部摄像头</option>
                                    <option value="1">摄像头 1</option>
                                    <option value="2">摄像头 2</option>
                                </select>
                            </div>
                        </div>

                        <!-- 位置范围筛选 -->
                        <div class="filter-group">
                            <label class="filter-label">X坐标范围 (m)</label>
                            <div class="filter-controls">
                                <input type="number" id="minXFilter" class="filter-input" placeholder="最小X" step="0.01">
                                <span class="filter-separator">至</span>
                                <input type="number" id="maxXFilter" class="filter-input" placeholder="最大X" step="0.01">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Y坐标范围 (m)</label>
                            <div class="filter-controls">
                                <input type="number" id="minYFilter" class="filter-input" placeholder="最小Y" step="0.01">
                                <span class="filter-separator">至</span>
                                <input type="number" id="maxYFilter" class="filter-input" placeholder="最大Y" step="0.01">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Z坐标范围 (m)</label>
                            <div class="filter-controls">
                                <input type="number" id="minZFilter" class="filter-input" placeholder="最小Z" step="0.01">
                                <span class="filter-separator">至</span>
                                <input type="number" id="maxZFilter" class="filter-input" placeholder="最大Z" step="0.01">
                            </div>
                        </div>

                        <!-- 重置按钮 -->
                        <div class="filter-group">
                            <button class="btn btn-secondary" id="resetFiltersBtn"><i class="fas fa-undo"></i> 重置筛选</button>
                        </div>
                    </div>
                </section>

                <!-- 三维轨迹可视化 -->
                <section class="trajectory-viewer">
                    <div class="panel-header">
                        <h2><i class="fas fa-cube"></i> 三维轨迹可视化</h2>
                        <div id="trajectory-status" class="status-message">等待轨迹数据...</div>
                    </div>
                    <div id="trajectory-visualizer-container" class="trajectory-canvas-container">
                        <!-- 3D Canvas will be inserted here by Three.js -->
                    </div>
                </section>

                <!-- 数据分析网格 -->
                <div class="analysis-grid">
                    <!-- 三维坐标历史 -->
                    <section class="coordinates-display">
                        <div class="panel-header">
                            <h2><i class="fas fa-table"></i> 坐标数据</h2>
                            <div class="coordinate-status" id="coordStatus">
                                <div class="status-dot status-disconnected" id="coordStatusDot"></div>
                                <span id="coordStatusText">等待数据</span>
                            </div>
                        </div>
                        <div class="coordinates-content">
                             <div class="coordinates-table-container">
                                <table class="coordinates-table">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-hashtag"></i> ID</th>
                                            <th><i class="fas fa-arrows-alt-h"></i> X (m)</th>
                                            <th><i class="fas fa-arrows-alt-v"></i> Y (m)</th>
                                            <th><i class="fas fa-arrow-up"></i> Z (m)</th>
                                            <th><i class="fas fa-percentage"></i> 置信度</th>
                                            <th><i class="fas fa-clock"></i> 时间</th>
                                        </tr>
                                    </thead>
                                    <tbody id="coordinatesTableBody">
                                        <!-- Coordinate data will be populated by JS -->
                                        <tr class="no-data-row">
                                            <td colspan="6">
                                                <i class="fas fa-info-circle"></i>
                                                暂无三维坐标数据
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>

                    <!-- 数据库浏览器 -->
                    <section class="db-explorer">
                         <div class="panel-header">
                            <h2><i class="fas fa-database"></i> 数据查询</h2>
                            <div id="db-status-message" class="status-message"></div>
                        </div>
                        <div class="db-explorer-panel">
                            <div class="query-section">
                                <textarea id="sql-query-input" class="sql-input" rows="3" placeholder="输入 SQL 查询语句...">SELECT id, timestamp_ms, pos_x, pos_y, pos_z, speed FROM trajectory ORDER BY id DESC LIMIT 10;</textarea>
                                <button id="execute-sql-btn" class="btn"><i class="fas fa-play"></i> 执行查询</button>
                            </div>
                            <div id="db-results-container" class="results-container">
                                <div class="no-results">
                                    <i class="fas fa-search"></i>
                                    <p>执行查询以查看结果</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>

        <!-- 底部信息 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-actions">
                    <button class="btn" id="reconnect-button"><i class="fas fa-wifi"></i> 重新连接</button>
                    <button class="btn" id="fullscreen-button"><i class="fas fa-expand"></i> 全屏模式</button>
                </div>
                <div class="footer-info">
                    <p><i class="fas fa-copyright"></i> 2024 乒乓球自动裁判系统</p>
                    <p><i class="fas fa-clock"></i> 最后更新: <span id="lastUpdate">--</span></p>
                </div>
            </div>
        </footer>
    </div>

    <!-- 模态放大显示 -->
    <div id="modal" class="modal">
        <span id="modalClose" class="close-btn">&times;</span>
        <canvas id="modalCanvas" class="modal-content"></canvas>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification-container"></div>

    <script type="module" src="simple-video.js"></script>
    <!-- 轨迹修复测试脚本 (仅在调试模式下加载) -->
    <script>
        // 检查是否启用调试模式
        if (window.location.search.includes('debug=trajectory') || window.location.search.includes('test=trajectory')) {
            const testScript = document.createElement('script');
            testScript.src = 'trajectory-fix-test.js';
            testScript.defer = true;
            document.head.appendChild(testScript);
            console.log('🧪 轨迹测试模式已启用');
        }
    </script>
</body>
</html>